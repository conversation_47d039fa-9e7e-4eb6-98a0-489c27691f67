package com.ebon.energy.fms.controller;

import com.ebon.energy.fms.domain.vo.JsonResult;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import com.ebon.energy.fms.domain.vo.alert.SeverityCategoryModel;
import com.ebon.energy.fms.repository.AlertRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警管理控制器
 * 从C# AlertsController转换而来
 */
@Slf4j
@RestController
@RequestMapping("/api/alerts")
public class AlertsController {

    @Resource
    private AlertRepository alertRepository;

    /**
     * 根据时间段获取告警汇总信息
     * GET: /api/alerts/summary
     */
    @GetMapping("/summary")
    public JsonResult<SeverityCategoryModel> getAlertSummaryByPeriod(@RequestParam(required = false) Integer timeRange) {
        try {
            LocalDateTime dateRange = LocalDateTime.now().toLocalDate().atStartOfDay();
            
            // 获取所有告警数据
            List<AlertModel> alerts = alertRepository.getAllAlerts();
            
            // 如果指定了时间范围，则过滤数据
            if (timeRange != null) {
                dateRange = dateRange.minusMinutes(timeRange);
                final LocalDateTime finalDateRange = dateRange;
                alerts = alerts.stream()
                    .filter(alert -> alert.getLastModifiedOnUtc().isAfter(finalDateRange))
                    .collect(Collectors.toList());
            }
            
            // 构建严重性汇总数据
            SeverityCategoryModel severitySummary = new SeverityCategoryModel();
            
            // 计算各严重级别的总数
            severitySummary.setTotalSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1).count());
            severitySummary.setTotalSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2).count());
            severitySummary.setTotalSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3).count());
            
            // 计算新告警数量
            severitySummary.setNewSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1 && "New".equals(x.getState())).count());
            severitySummary.setNewSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2 && "New".equals(x.getState())).count());
            severitySummary.setNewSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3 && "New".equals(x.getState())).count());
            
            // 计算已关闭告警数量
            severitySummary.setClosedSev1((int) alerts.stream().filter(x -> x.getSeverity() == 1 && "Closed".equals(x.getState())).count());
            severitySummary.setClosedSev2((int) alerts.stream().filter(x -> x.getSeverity() == 2 && "Closed".equals(x.getState())).count());
            severitySummary.setClosedSev3((int) alerts.stream().filter(x -> x.getSeverity() == 3 && "Closed".equals(x.getState())).count());
            
            return JsonResult.buildSuccess(severitySummary);
            
        } catch (Exception e) {
            log.error("获取告警汇总信息失败", e);
            return JsonResult.buildError("获取告警汇总信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取过滤后的告警数据
     * GET: /api/alerts/data
     */
    @GetMapping("/data")
    public JsonResult<List<AlertModel>> getAlertData(
            @RequestParam int timeRange,
            @RequestParam String sev,
            @RequestParam String monitoringConditions,
            @RequestParam String states) {
        
        try {
            LocalDateTime dateRange = LocalDateTime.now().toLocalDate().atStartOfDay().minusMinutes(timeRange);
            
            // 解析过滤条件
            String[] monitoringConditionsArray = monitoringConditions.split(",");
            String[] severityArray = sev.split(",");
            String[] statesArray = states.split(",");
            
            // 获取过滤后的告警数据
            List<AlertModel> alerts = alertRepository.getFilteredAlerts(
                monitoringConditionsArray, 
                severityArray, 
                statesArray
            );
            
            // 按时间过滤并排序
            List<AlertModel> filteredAlerts = alerts.stream()
                .filter(alert -> alert.getLastModifiedOnUtc().isAfter(dateRange))
                .sorted((a, b) -> b.getCreatedOnUtc().compareTo(a.getCreatedOnUtc()))
                .collect(Collectors.toList());
            
            return JsonResult.buildSuccess(filteredAlerts);
            
        } catch (Exception e) {
            log.error("获取告警数据失败", e);
            return JsonResult.buildError("获取告警数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取告警详情
     * GET: /api/alerts/{alertId}
     */
    @GetMapping("/{alertId}")
    public JsonResult<AlertDetails> getAlertDataById(@PathVariable String alertId) {
        try {
            AlertDetails alertDetails = alertRepository.getAlertDetailsById(alertId);
            
            if (alertDetails == null || alertDetails.getDetail() == null) {
                return JsonResult.buildError("未找到指定的告警信息");
            }

            return JsonResult.buildSuccess(alertDetails);
            
        } catch (Exception e) {
            log.error("获取告警详情失败, alertId: {}", alertId, e);
            return JsonResult.buildError("获取告警详情失败: " + e.getMessage());
        }
    }
}
