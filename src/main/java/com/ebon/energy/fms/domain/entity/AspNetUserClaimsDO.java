package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AspNetUserClaims 实体类
 */
@Data
@Accessors(chain = true)
@TableName("AspNetUserClaims")
public class AspNetUserClaimsDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 声明类型
     */
    private String claimType;

    /**
     * 声明值
     */
    private String claimValue;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifiedUtc;
}
