package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@TableName("AspNetUsers")
public class AspNetUserDO {

    @TableId("Id")
    private String id;

    @TableField("Email")
    private String email;

    @TableField("EmailConfirmed")
    private Boolean emailConfirmed;

    @TableField("PasswordHash")
    private String passwordHash;

    @TableField("SecurityStamp")
    private String securityStamp;

    @TableField("PhoneNumber")
    private String phoneNumber;

    @TableField("PhoneNumberConfirmed")
    private Boolean phoneNumberConfirmed;

    @TableField("TwoFactorEnabled")
    private Boolean twoFactorEnabled;

    @TableField("LockoutEndDateUtc")
    private Timestamp lockoutEndDateUtc;

    @TableField("LockoutEnabled")
    private Boolean lockoutEnabled;

    @TableField("AccessFailedCount")
    private int accessFailedCount;

    @TableField("UserName")
    private String userName;

    @TableField("AddressId")
    private Integer addressId;

    @TableField(value = "LastModifiedUtc")
    private Timestamp lastModifiedUtc;

}