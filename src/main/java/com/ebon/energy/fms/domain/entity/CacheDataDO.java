package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 缓存表
 */
@Data
@TableName("CacheData")
@AllArgsConstructor
public class CacheDataDO {

    @TableField(value = "KeyValue")
    private String keyValue;

    @TableField(value = "DataValue")
    private String dataValue;

    @TableField(value = "ExpireAt")
    private Timestamp expireAt;
}
