package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Cms配置实体类
 */
@Data
@TableName("CmsConfig")
public class CmsConfigDO {
    
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField("Name")
    private String name;

    /**
     * 类型 1-Banner
     */
    @TableField("Type")
    private Integer type;

    /**
     * 配置内容
     */
    @TableField("Content")
    private String content;

    /**
     * 0-禁用 1-启用
     */
    @TableField("Status")
    private Integer status;

    /**
     * 创建人ID
     */
    @TableField(value = "CreatedBy")
    private Integer createdBy;

    /**
     * 更新人ID
     */
    @TableField(value = "UpdatedBy")
    private Integer updatedBy;

    /**
     * 创建时间
     */
    @TableField("CreatedAt")
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    @TableField("UpdatedAt")
    private Timestamp updatedAt;
}