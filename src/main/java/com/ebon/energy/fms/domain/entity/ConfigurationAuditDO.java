package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.UUID;

/**
 * 配置审计表实体类
 * 对应C#中的ConfigurationAudit表
 */
@Data
@NoArgsConstructor
@TableName("ConfigurationsAudit")
public class ConfigurationAuditDO {

    /**
     * 审计ID，自增主键
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private String id;

    @TableField(value = "ConfigurationId")
    private Integer configurationId;

    /**
     * 产品序列号
     */
    @TableField("RedbackProductSn")
    private String redbackProductSn;

    /**
     * 配置类型
     */
    @TableField("ConfigurationType")
    private Integer configurationType;

    /**
     * 配置内容
     */
    @TableField("Configurations")
    private String configurations;

    /**
     * 设备端配置内容
     */
    @TableField("ConfigurationsOnDevice")
    private String configurationsOnDevice;

    /**
     * 修改时间
     */
    @TableField("ModifiedDateTime")
    private Timestamp modifiedDateTime;

    /**
     * 最后修改人ID
     */
    @TableField("LastModifiedById")
    private String lastModifiedById;

    @TableField("RowVersion")
    private byte[] rowVersion;

    /**
     * 根据ConfigurationsDO创建审计记录的构造函数
     * 
     * @param configuration 配置实体
     */
    public ConfigurationAuditDO(ConfigurationsDO configuration) {
        if (configuration != null) {
            this.id= UUID.randomUUID().toString();
            this.configurationId = configuration.getId();
            this.redbackProductSn = configuration.getRedbackProductSn();
            this.configurationType = configuration.getConfigurationType();
            this.configurations = configuration.getConfigurations();
            this.configurationsOnDevice = configuration.getConfigurationsOnDevice();
            this.modifiedDateTime = configuration.getModifiedDateTime();
            this.lastModifiedById = configuration.getLastModifiedById();
            this.rowVersion =configuration.getRowVersion();
        }
    }
}
