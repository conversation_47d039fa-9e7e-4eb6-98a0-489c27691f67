package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("Configurations")
public class ConfigurationsDO {

    // 对应表中的 Id 字段，自增长主键
    @TableId("Id")
    private Integer id;

    @TableField("RedbackProductSn")
    private String redbackProductSn;

    @TableField("ConfigurationType")
    private int configurationType;

    @TableField("Configurations")
    private String configurations;

    @TableField("ModifiedDateTime")
    private Timestamp modifiedDateTime;

    @TableField("ConfigurationsOnDevice")
    private String configurationsOnDevice;

    @TableField("LastModifiedById")
    private String lastModifiedById;

    // timestamp 字段，不能手动更新，在更新时忽略此字段
    @TableField(value = "RowVersion", updateStrategy = FieldStrategy.NEVER)
    private byte[] rowVersion;

}
