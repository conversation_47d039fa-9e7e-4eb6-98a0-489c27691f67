package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.domain.vo.DailySiteTotalsVO;
import com.ebon.energy.fms.domain.vo.EnergyVal;
import com.ebon.energy.fms.domain.vo.WattHour;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class DailyTotalVO {
    
    private String serialNumber;
    
    private String date;
    
    private Timestamp dateDate;
    
    private Integer dailyUsageWh;
    
    private Integer dailySoldWh;
    
    private Integer dailyBoughtWh;
    
    private Integer dailyGenerationWh;

    private Integer dailyBatteryChargedWh;

    private Integer dailyBatteryDischargedWh;

    private Boolean dailyUsageAdjusted;

    private Boolean dailySoldAdjusted;

    private Boolean dailyBoughtAdjusted;

    private Boolean dailyGenerationAdjusted;

    private Boolean dailyBatteryChargedAdjusted;

    private Boolean dailyBatteryDischargedAdjusted;

    public DailySiteTotalsVO asPublicDto() {
        // 处理每日使用量（取反调整状态作为准确性）
        EnergyVal dailyUsage = new EnergyVal(
                dailyUsageWh,
                !dailyUsageAdjusted // isAccurate = !调整状态
        );

        // 处理每日售电量
        EnergyVal dailySold = new EnergyVal(
                dailySoldWh,
                !dailySoldAdjusted
        );

        // 处理每日购电量
        EnergyVal dailyBought = new EnergyVal(
                dailyBoughtWh,
                !dailyBoughtAdjusted
        );

        // 处理每日发电量
        EnergyVal dailyGeneration = new EnergyVal(
                dailyGenerationWh,
                !dailyGenerationAdjusted
        );

        // 处理电池充电量（可空值处理）
        EnergyVal dailyBatteryCharged = null;
        if (dailyBatteryChargedWh != null) {
            boolean isChargedAccurate = !(dailyBatteryChargedAdjusted != null && dailyBatteryChargedAdjusted);
            dailyBatteryCharged = new EnergyVal(dailyBatteryChargedWh, isChargedAccurate);
        }

        // 处理电池放电量（可空值处理）
        EnergyVal dailyBatteryDischarged = null;
        if (dailyBatteryDischargedWh != null) {
            boolean isDischargedAccurate = !(dailyBatteryDischargedAdjusted != null && dailyBatteryDischargedAdjusted);
            dailyBatteryDischarged = new EnergyVal(dailyBatteryDischargedWh, isDischargedAccurate);
        }

        return new DailySiteTotalsVO(
                date,
                dailyUsage,
                dailySold,
                dailyBought,
                dailyGeneration,
                dailyBatteryCharged,
                dailyBatteryDischarged
        );
    }
}