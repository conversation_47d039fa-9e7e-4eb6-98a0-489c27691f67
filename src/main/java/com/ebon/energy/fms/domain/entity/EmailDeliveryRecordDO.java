package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("EmailDeliveryRecord")
public class EmailDeliveryRecordDO {
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("Subject")
    private String subject;

    @TableField("Content")
    private String content;

    @TableField("FromAddress")
    private String fromAddress;

    @TableField("ToAddress")
    private String toAddress;

    @TableField("Status")
    private String status;

    @TableField("FailReason")
    private String failReason;

    @TableField(value = "CreatedOnUtc")
    private LocalDateTime createdOnUtc;

    @TableField(value = "SerialNumber")
    private String serialNumber;
}
