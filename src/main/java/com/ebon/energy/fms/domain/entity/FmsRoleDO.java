package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 角色表
 */
@Data
@TableName("FmsRole")
public class FmsRoleDO {

    /**
     * Id
     */
    private Integer id;
    
    /**
     * 角色名
     */
    private String roleName;

    /**
     * 数据权限类型 0-All 1-Owner 2-Group 3-Self
     */
    private Integer dataPermission;
    
    /**
     * 状态：true 启用；false 禁用
     */
    private boolean status;
    
    /**
     * 创建时间
     */
    private Timestamp createdAt;
    
    /**
     * 修改时间
     */
    private Timestamp updatedAt;

    public boolean isEnable(){
        return this.status;
    }
}
