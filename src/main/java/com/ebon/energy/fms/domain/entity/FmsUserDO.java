package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;

import java.sql.Timestamp;

/**
 * 用户表
 */
@Data
@TableName("FmsUser")
public class FmsUserDO {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 姓名
     */
    @TableField(value = "Name")
    private String name;
    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 密码
     */
    @TableField(value = "password")
    private String password;


    /**
     * redback portal用户id
     */
    @TableField(value = "PortalUserId")
    private String portalUserId;

    /**
     * 最后登录时间
     */
    @TableField(value = "LastLoginTime")
    private Timestamp lastLoginTime;

    /**
     * 状态
     */
    @Getter
    @TableField(value = "Status")
    private Boolean status;

}