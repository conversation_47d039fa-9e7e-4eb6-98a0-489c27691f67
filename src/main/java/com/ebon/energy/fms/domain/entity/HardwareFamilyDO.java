package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 硬件系列实体类
 */
@Data
@Accessors(chain = true)
@TableName("HardwareFamily")
public class HardwareFamilyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "Id")
    private Integer id;

    /**
     * 系列名称
     */
    @TableField("Name")
    private String name;

    /**
     * 短名称
     */
    @TableField("ShortName")
    private String shortName;

    /**
     * 系统类型
     */
    @TableField("SystemType")
    private String systemType;

    /**
     * 是否在大型安装商中显示
     */
    @TableField("DisplayInLargeInstaller")
    private Boolean displayInLargeInstaller;

    /**
     * 营销显示名称
     */
    @TableField("MarketingDisplayName")
    private String marketingDisplayName;

    /**
     * 营销短代码
     */
    @TableField("MarketingShortCode")
    private String marketingShortCode;

    /**
     * 获取系列显示名称
     * 优先使用营销显示名称，否则使用名称
     */
    public String getFamilyDisplayName() {
        return marketingDisplayName != null ? marketingDisplayName : name;
    }
}
