package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("InverterErrorStatistics") 
public class InverterErrorStatisticsDO {

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("RedbackProductSn")
    private String redbackProductSn;

    @TableField("Date")
    private String date;

    @TableField("ErrorCode")
    private String errorCode;

    @TableField("Count")
    private Integer count;

    @TableField("UpdatedAt")
    private Timestamp updatedAt;

}