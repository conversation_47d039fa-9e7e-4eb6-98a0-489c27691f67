package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("InverterErrorStatisticsDetail") 
public class InverterErrorStatisticsDetailDO {

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("StatisticsId")
    private Integer statisticsId;

    @TableField("FirstTime")
    private String firstTime;

    @TableField("LastTime")
    private String lastTime;

    @TableField("UpdatedAt")
    private Timestamp updatedAt;

}