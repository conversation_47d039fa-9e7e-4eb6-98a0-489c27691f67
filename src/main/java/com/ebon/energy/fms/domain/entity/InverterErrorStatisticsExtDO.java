package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class InverterErrorStatisticsExtDO {

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;
    
    @TableField("RedbackProductSn")
    private String redbackProductSn;

    @TableField("Date")
    private String date;

    @TableField("ErrorCode")
    private String errorCode;

    @TableField("Count")
    private Integer count;

    private Integer lastDetailId;

    private String lastTime;

    private List<InverterErrorStatisticsDetailDO> details = new ArrayList<>();

    public void addDetail(InverterErrorStatisticsDetailDO detailDO) {
        details.add(detailDO);
    }

}