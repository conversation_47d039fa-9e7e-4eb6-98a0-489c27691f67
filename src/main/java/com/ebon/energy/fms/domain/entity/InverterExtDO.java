package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
public class InverterExtDO {

    @TableField(value = "SerialNumber")
    private String serialNumber;

    @TableField(value = "IsInstalled")
    private Boolean isInstalled;

    @TableField(value = "Long")
    private String longitude;

    @TableField(value = "Lat")
    private String latitude;

    @TableField(value = "Address")
    private String address;

    @TableField(value = "ProductOwner")
    private String productOwner;

    @TableField(value = "OffComms")
    private Boolean offComms;

    @TableField(value = "State")
    private String state;

    @TableField(value = "ActiveInstallationDate")
    private Date activeInstallationDate;

    @TableField(value = "InverterFirmware")
    private String inverterFirmware;

    @TableField(value = "ROSSVersion")
    private String rossVersion;

    @TableField(value = "WindowsVersion")
    private String windowsVersion;

    @TableField(value = "Comment")
    private String comment;

    @TableField(value = "DetectedBatteryManufacturer")
    private String detectedBatteryManufacturer;

    @TableField(value = "DetectedBatteryModel")
    private String detectedBatteryModel;

    @TableField(value = "IsHourlyOnline")
    private Boolean isHourlyOnline;

    @TableField(value = "IsDailyOnline")
    private Boolean isDailyOnline;
    
    @TableField(value = "IsSCCMOnline")
    private Boolean isSCCMOnline;

    @TableField(value = "HasBMS")
    private Boolean hasBMS;

    @TableField(value = "SoC")
    private Double soc;

    @TableField(value = "SystemStatus")
    private String systemStatus;

    @TableField(value = "OriginalInstaller")
    private String originalInstaller;

    @TableField(value = "MaintainingInstaller")
    private String maintainingInstaller;

    @TableField(value = "ElectricalConfiguration")
    private String electricalConfiguration;

    @TableField(value = "WatchDogVersion")
    private String watchDogVersion;

    @TableField(value = "SCCMHeartbeatTimeStamp")
    private Date sccmHeartbeatTimeStamp;

    @TableField(value = "IsInWarranty")
    private Boolean isInWarranty;

    @TableField(value = "SystemStatusTimeStamp_Brisbane")
    private Date systemStatusTimeStampBrisbane;

    @TableField(value = "LastElectricalUpdateTimestamp_Brisbane")
    private Date lastElectricalUpdateTimestampBrisbane;

    @TableField(value = "BatteryChargeVoltage_BMS")
    private Double batteryChargeVoltageBMS;

    @TableField(value = "BatteryChargeVoltage_Custom")
    private Double batteryChargeVoltageCustom;

    @TableField(value = "OriginalInstallerEmail")
    private String originalInstallerEmail;

    @TableField(value = "MaintainingInstallerEmail")
    private String maintainingInstallerEmail;

    @TableField(value = "ProductOwnerEmail")
    private String productOwnerEmail;

    @TableField(value = "Suburb")
    private String suburb;

    @TableField(value = "Postcode")
    private String postcode;

    @TableField(value = "InverterMode")
    private String inverterMode;

    @TableField(value = "TimeZone")
    private String timeZone;

    @TableField(value = "BatteryDisChargeCurrent_BMS")
    private Double batteryDisChargeCurrentBMS;

    @TableField(value = "BatteryChargeCurrent_BMS")
    private Double batteryChargeCurrentBMS;

    @TableField(value = "BatteryDisChargeCurrent_Custom")
    private Double batteryDisChargeCurrentCustom;

    @TableField(value = "BatteryChargeCurrent_Custom")
    private Double batteryChargeCurrentCustom;

    @TableField(value = "BatteryChargeCurrent_Override")
    private Double batteryChargeCurrentOverride;

    @TableField(value = "BatteryDisChargeCurrent_Override")
    private Double batteryDisChargeCurrentOverride;

    @TableField(value = "MinimumSoC")
    private Double minimumSoC;

    @TableField(value = "MinimumSoCOffGrid")
    private Double minimumSoCOffGrid;

    @TableField(value = "BatteryCapacity")
    private Double batteryCapacity;

    @TableField(value = "OffGridCharge")
    private Boolean offGridCharge;

    @TableField(value = "BackupOn")
    private Boolean backupOn;

    @TableField(value = "LimitExportPower")
    private Double limitExportPower;

    @TableField(value = "LimitExportPowerUserValue")
    private Double limitExportPowerUserValue;

    @TableField(value = "IsLimitExportPower")
    private Boolean isLimitExportPower;

    @TableField(value = "FanMode")
    private String fanMode;

    @TableField(value = "OwnerPhoneNumber")
    private String ownerPhoneNumber;

    @TableField(value = "WindowsVersion_SS")
    private String windowsVersionSS;

    @TableField(value = "CTComms")
    private Boolean ctComms;

    @TableField(value = "GridVoltage")
    private Double gridVoltage;

    @TableField(value = "InverterTime")
    private Date inverterTime;

    @TableField(value = "WindowsTime")
    private Date windowsTime;

    @TableField(value = "LastUpdateBrisbane")
    private Date lastUpdateBrisbane;

    @TableField(value = "FrozenBatteryCount")
    private Integer frozenBatteryCount;

    @TableField(value = "InverterModelName")
    private String inverterModelName;

    @TableField(value = "IsHourlyOnline_WD")
    private Boolean isHourlyOnlineWD;

    @TableField(value = "IsDailyOnline_WD")
    private Boolean isDailyOnlineWD;

    @TableField(value = "WDHeartbeatTimeStamp_Brisbane")
    private Date wdHeartbeatTimeStampBrisbane;

    @TableField(value = "BatteryPower")
    private Double batteryPower;

    @TableField(value = "PVPower")
    private Double pvPower;

    @TableField(value = "PVTotalToday")
    private Double pvTotalToday;

    @TableField(value = "PVTotalAllTime")
    private Double pvTotalAllTime;

    @TableField(value = "ACPower")
    private Double acPower;

    @TableField(value = "ACTotalToday")
    private Double acTotalToday;

    @TableField(value = "GridPower")
    private Double gridPower;

    @TableField(value = "DayTotalImport")
    private Double dayTotalImport;

    @TableField(value = "DayTotalExport")
    private Double dayTotalExport;

    @TableField(value = "AllTimeTotalImport")
    private Double allTimeTotalImport;

    @TableField(value = "AllTimeTotalExport")
    private Double allTimeTotalExport;

    @TableField(value = "RossDeviceId")
    private String rossDeviceId;

    @TableField(value = "WatchdogDeviceId")
    private String watchdogDeviceId;

    @TableField(value = "MaintainingInstallerPhoneNumber")
    private String maintainingInstallerPhoneNumber;

    @TableField(value = "BMSVersionNumber")
    private String bmsVersionNumber;
    
    private Integer batteryStatus;
    
    private String edgeId;
    
    private Boolean isOnline;
    
    private Boolean isWatchdogOnline;

}