package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 权限实体类
 */
@Data
@TableName("Permission")
public class PermissionDO {
    
    /**
     * 权限ID
     */
    @TableId("Id")
    private Integer id;

    /**
     * 权限编码
     */
    @TableField("PermissionCode")
    private String permissionCode;

    /**
     * 权限名称
     */
    @TableField("PermissionName")
    private String permissionName;

    /**
     * 权限类型：1-模块，2-功能
     */
    @TableField("PermissionType")
    private Integer permissionType;

    /**
     * 父权限ID
     */
    @TableField("ParentId")
    private Integer parentId;

    /**
     * 接口Url
     */
    @TableField("Url")
    private String url;

    /**
     * 显示顺序
     */
    @TableField("SortOrder")
    private Integer sortOrder;

    /**
     * 状态：1-启用，0-禁用
     */
    @TableField("Status")
    private Boolean status;

    /**
     * 权限描述
     */
    @TableField("Description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("CreatedAt")
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    @TableField("UpdatedAt")
    private Timestamp updatedAt;
}