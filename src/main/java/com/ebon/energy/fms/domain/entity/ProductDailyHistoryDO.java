package com.ebon.energy.fms.domain.entity;

import lombok.Data;
import com.ebon.energy.fms.domain.vo.site.ProductDailyCacheModel;
import com.ebon.energy.fms.domain.vo.WattHour;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
public class ProductDailyHistoryDO {

    private String serialNumber;

    private String date;

    private Timestamp dateDate;

    private int dailyUsageWh;

    private int dailySoldWh;

    private int dailyBoughtWh;

    private int dailyGenerationWh;

    public Integer dailyBatteryChargedWh;

    public Integer dailyBatteryDischargedWh;

    public Boolean dailyUsageAdjusted;

    public Boolean dailySoldAdjusted;

    public Boolean dailyBoughtAdjusted;

    public Boolean dailyGenerationAdjusted;

    public Boolean dailyBatteryChargedAdjusted;

    public Boolean dailyBatteryDischargedAdjusted;

    /**
     * 转换为ProductDailyCacheModel
     *
     * @param serialNumber 序列号
     * @return ProductDailyCacheModel实例
     */
    public ProductDailyCacheModel asProductDailyCache(String serialNumber) {
        // 将Timestamp转换为LocalDateTime，如果为null则使用最小值
        LocalDateTime time = dateDate != null ? dateDate.toLocalDateTime() : LocalDateTime.MIN;

        // 将Wh转换为kWh（除以1000）
        double totalConsumptionKwh = dailyUsageWh / WattHour.Kilo.getValue().doubleValue();
        double totalExportKwh = dailySoldWh / WattHour.Kilo.getValue().doubleValue();
        double totalImportKwh = dailyBoughtWh / WattHour.Kilo.getValue().doubleValue();
        double totalGenerationKwh = dailyGenerationWh / WattHour.Kilo.getValue().doubleValue();

        // 处理可能为null的电池数据
        double batteryChargedkWh = dailyBatteryChargedWh != null ?
            (dailyBatteryChargedWh / WattHour.Kilo.getValue().doubleValue()) : 0.0;
        double batteryDischargedkWh = dailyBatteryDischargedWh != null ?
            (dailyBatteryDischargedWh / WattHour.Kilo.getValue().doubleValue()) : 0.0;

        return new ProductDailyCacheModel(
                serialNumber,
                time,
                totalConsumptionKwh,
                totalExportKwh,
                totalImportKwh,
                totalGenerationKwh,
                batteryChargedkWh,
                batteryDischargedkWh
        );
    }

}
