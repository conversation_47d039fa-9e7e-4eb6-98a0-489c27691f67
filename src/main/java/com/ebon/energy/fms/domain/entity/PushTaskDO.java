package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 推送任务实体类
 */
@Data
@Accessors(chain = true)
@TableName("PushTask")
public class PushTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 目标操作系统: ALL / IOS / ANDROID
     */
    @TableField(value = "os")
    private String os;

    /**
     * 客户端版本号列表，逗号分隔；ALL 表示全部版本
     */
    @TableField(value = "version_list")
    private String versionList;

    /**
     * 推送标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 推送内容正文
     */
    @TableField(value = "content")
    private String content;

    /**
     * 计划发送时间（Unix 毫秒时间戳）；0 或 NULL 代表立即发送
     */
    @TableField(value = "push_time")
    private Long pushTime;

    /**
     * 任务状态: PENDING / SENT / FAILED
     */
    @TableField(value = "status")
    private String status;

    /**
     * 已重试次数（发送失败时递增）
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 任务创建时间（Unix 毫秒时间戳）
     */
    @TableField(value = "created_at")
    private Long createdAt;

    /**
     * 最后更新时间（Unix 毫秒时间戳）
     */
    @TableField(value = "updated_at")
    private Long updatedAt;

    /**
     * 创建人用户名 / ID
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 最后更新人用户名 / ID
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 推送任务状态枚举
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String SENT = "SENT";
        public static final String FAILED = "FAILED";
    }

    /**
     * 操作系统类型枚举
     */
    public static class OS {
        public static final String ALL = "ALL";
        public static final String IOS = "IOS";
        public static final String ANDROID = "ANDROID";
    }

    /**
     * 版本列表常量
     */
    public static class Version {
        public static final String ALL = "ALL";
    }
}
