package com.ebon.energy.fms.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("RedbackProductInstallations")
public class RedbackProductInstallationDO {

    @TableId(value = "Id")
    private Integer id;

    @TableField(value = "RedbackProductSn")
    private String redbackProductSn;

    @TableField(value = "AddressId")
    private Integer addressId;

    @TableField(value = "InstallationStartDate")
    private Timestamp installationStartDate;

    @TableField(value = "InstallationEndDate")
    private Timestamp installationEndDate;

    @TableField(value = "InstallationInstallerCompany")
    private String installationInstallerCompany;

    @TableField(value = "SiteId")
    private String siteId;

    @TableField(value = "ReplacedBySerialNumber")
    private String replacedBySerialNumber;

    @TableField(value = "WarrantyEndDate")
    private Timestamp warrantyEndDate;

    @TableField(value = "Phase")
    private String phase;

    @TableField(value = "PhaseRole")
    private String phaseRole;
}
