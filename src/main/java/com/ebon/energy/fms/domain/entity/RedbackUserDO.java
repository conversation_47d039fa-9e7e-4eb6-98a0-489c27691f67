package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("RedbackUsers")
public class RedbackUserDO {

    @TableField(value = "Id")
    private String id;

    @TableField(value = "UserType")
    private Integer userType;

    @TableField(value = "InstallerCompanyId")
    private String installerCompanyId;

    @TableField(value = "Value")
    private String value;

}