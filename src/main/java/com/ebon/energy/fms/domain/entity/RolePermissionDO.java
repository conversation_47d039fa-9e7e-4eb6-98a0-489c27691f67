package com.ebon.energy.fms.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 角色-权限关联实体类
 */
@Data
@TableName("RolePermission")
public class RolePermissionDO {
    
    /**
     * 角色ID
     */
    @TableField("RoleId")
    private Integer roleId;

    /**
     * 权限ID
     */
    @TableField("PermissionId")
    private Integer permissionId;

    /**
     * 创建人ID
     */
    @TableField("CreatedBy")
    private Integer createdBy;

    /**
     * 创建时间
     */
    @TableField("CreatedAt")
    private Timestamp createdAt;

    /**
     * 更新时间
     */
    @TableField("UpdatedAt")
    private Timestamp updatedAt;
}