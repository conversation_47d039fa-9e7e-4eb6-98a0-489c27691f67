package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.enums.ManufacturerEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class BatteryManagerDesiredSettingsVO {

    @JsonProperty("Manufacturer")
    private ManufacturerEnum Manufacturer;

    @JsonProperty("TotalCapacityAh")
    private Integer TotalCapacityAh;

    @JsonProperty("BatteryCount")
    private Integer BatteryCount;

    @JsonProperty("MaxChargeVoltage")
    private Integer maxDischargeCurrent;

    @JsonProperty("MinSoCPercent")
    private Integer minSoCPercent;

    @JsonProperty("InterlocksEnabled")
    private Boolean interlocksEnabled;

    @JsonProperty("PerInterlockEnabled")
    public Map<String, Boolean> perInterlockEnabled;


    public BatteryManagerDesiredSettingsVO(ManufacturerEnum manufacturer, Integer totalCapacityAh) {
        this.Manufacturer = manufacturer;
        this.TotalCapacityAh = totalCapacityAh;
    }

    public static BatteryManagerDesiredSettingsVO getDefault() {
        return new BatteryManagerDesiredSettingsVO(
                ManufacturerEnum.None,
                null
        );
    }


    public static String MaxChargeVoltageName = "maxChargeVoltage";
    public static String MinDischargeVoltageName = "minDischargeVoltage";
    public static String MaxChargeCurrentName = "maxChargeCurrent";
    public static String MaxDischargeCurrentName = "maxDischargeCurrent";
    public static String MaxOffgridDischargeCurrentName = "maxOffgridDischargeCurrent";
    public static String MinSOCPercentName = "minStateOfChargePercent";
}
