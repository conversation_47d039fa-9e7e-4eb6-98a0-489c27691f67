// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

/**
 * 电气控制器数据DTO类
 * 从C#的ElectricalControllerDataDto类转换而来
 */
@Data
public class ElectricalControllerDataDto {

    @JsonProperty("IsReadOnly")
    private Boolean isReadOnly = false;

    @JsonProperty("ElectricalConfiguration")
    private ElectricalConfiguration electricalConfiguration;

    @JsonProperty("IsInSync")
    private Boolean isInSync;

    @JsonProperty("DeviceTimestamp")
    private ZonedDateTime deviceTimestamp;

    @JsonProperty("CloudTimestamp")
    private ZonedDateTime cloudTimestamp;

    /**
     * This indicates whether the device supports AC Coupled functionality, not if
     * it is turned on
     */
    @JsonProperty("IsACCoupledSupported")
    private Boolean isACCoupledSupported;

    @JsonProperty("IsWASafetyCountrySupported")
    private Boolean isWASafetyCountrySupported;

    @JsonProperty("IsGridOn")
    private Boolean isGridOn;

    @JsonProperty("ProductModelName")
    private String productModelName;
}