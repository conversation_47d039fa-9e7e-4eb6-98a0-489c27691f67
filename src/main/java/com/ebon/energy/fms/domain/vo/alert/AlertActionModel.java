// AlertActionModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 告警操作记录模型
 * 用于记录对告警执行的操作历史
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertActionModel {

    /**
     * 操作记录唯一标识ID
     */
    @JsonProperty("Id")
    private String id;

    /**
     * 关联的告警ID
     * 指向该操作所属的告警
     */
    @JsonProperty("AlertId")
    private String alertId;

    /**
     * 执行的操作类型
     * 如：确认、关闭、重新打开等
     */
    @JsonProperty("Action")
    private String action;

    /**
     * 操作执行结果
     * 操作是否成功及相关结果信息
     */
    @JsonProperty("Result")
    private String result;

    /**
     * 操作详细信息
     * 操作的具体描述和备注
     */
    @JsonProperty("Details")
    private String details;

    /**
     * 最后修改人ID
     * 最后一次修改该记录的用户ID
     */
    @JsonProperty("LastModifiedById")
    private String lastModifiedById;

    /**
     * 最后修改时间(UTC)
     * 记录最后一次修改的时间
     */
    @JsonProperty("LastModifiedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedOnUtc;

    /**
     * 创建人ID
     * 创建该操作记录的用户ID
     */
    @JsonProperty("CreatedById")
    private String createdById;

    /**
     * 创建时间(UTC)
     * 操作记录的创建时间
     */
    @JsonProperty("CreatedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOnUtc;
}
