// AlertDetails.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 告警详情模型
 * 包含告警的完整信息，包括基本信息、操作历史和相关历史数据
 */
@Data
public class AlertDetails {

    /**
     * 告警基本信息
     * 包含告警的详细信息
     */
    @JsonProperty("Detail")
    private AlertModel detail;

    /**
     * 告警操作记录列表
     * 对该告警执行的所有操作历史
     */
    @JsonProperty("Actions")
    private List<AlertActionModel> actions;

    /**
     * 相关历史告警数据
     * 同一设备的其他告警历史记录
     */
    @JsonProperty("HistoryData")
    private List<AlertModel> historyData;
}
