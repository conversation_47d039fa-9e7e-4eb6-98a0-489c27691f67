// AlertModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 告警模型
 * 用于表示系统中的告警信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertModel {

    /**
     * 告警唯一标识ID
     */
    @JsonProperty("Id")
    private String id;

    /**
     * 设备序列号
     * 标识产生告警的设备
     */
    @JsonProperty("SerialNumber")
    private String serialNumber;

    /**
     * 告警状态
     * 如：New(新建)、Closed(已关闭)等
     */
    @JsonProperty("State")
    private String state;

    /**
     * 监控条件
     * 具体的监控条件值
     */
    @JsonProperty("MonitorCondition")
    private String monitorCondition;

    /**
     * 监控条件类型
     * 监控条件的分类类型
     */
    @JsonProperty("MonitorConditionType")
    private String monitorConditionType;

    /**
     * 严重级别
     * 1-严重，2-警告，3-信息
     */
    @JsonProperty("Severity")
    private int severity;

    /**
     * 告警描述
     * 详细的告警描述信息
     */
    @JsonProperty("Description")
    private String description;

    /**
     * 创建时间(UTC)
     * 告警首次创建的时间: yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("CreatedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdOnUtc;

    /**
     * 最后修改时间(UTC)
     * 告警最后一次更新的时间: yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("LastModifiedOnUtc")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastModifiedOnUtc;
}
