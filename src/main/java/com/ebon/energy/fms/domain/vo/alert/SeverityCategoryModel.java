// SeverityCategoryModel.java
package com.ebon.energy.fms.domain.vo.alert;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 告警严重性分类统计模型
 * 用于统计不同严重级别告警的数量分布
 */
@Data
public class SeverityCategoryModel {

    /**
     * 严重级别1的告警总数
     * 包含所有状态的严重级别1告警
     */
    @JsonProperty("TotalSev1")
    private int totalSev1;

    /**
     * 严重级别2的告警总数
     * 包含所有状态的严重级别2告警
     */
    @JsonProperty("TotalSev2")
    private int totalSev2;

    /**
     * 严重级别3的告警总数
     * 包含所有状态的严重级别3告警
     */
    @JsonProperty("TotalSev3")
    private int totalSev3;

    /**
     * 新建状态的严重级别1告警数量
     */
    @JsonProperty("NewSev1")
    private int newSev1;

    /**
     * 新建状态的严重级别2告警数量
     */
    @JsonProperty("NewSev2")
    private int newSev2;

    /**
     * 新建状态的严重级别3告警数量
     */
    @JsonProperty("NewSev3")
    private int newSev3;

    /**
     * 已关闭状态的严重级别1告警数量
     */
    @JsonProperty("ClosedSev1")
    private int closedSev1;

    /**
     * 已关闭状态的严重级别2告警数量
     */
    @JsonProperty("ClosedSev2")
    private int closedSev2;

    /**
     * 已关闭状态的严重级别3告警数量
     */
    @JsonProperty("ClosedSev3")
    private int closedSev3;

    /**
     * 获取所有新建告警的总数
     * @return 所有严重级别的新建告警数量之和
     */
    @JsonProperty("TotalNewAlerts")
    public int getTotalNewAlerts() {
        return newSev1 + newSev2 + newSev3;
    }

    /**
     * 获取所有已关闭告警的总数
     * @return 所有严重级别的已关闭告警数量之和
     */
    @JsonProperty("TotalClosedAlerts")
    public int getTotalClosedAlerts() {
        return closedSev1 + closedSev2 + closedSev3;
    }

    /**
     * 获取告警总数
     * @return 新建告警和已关闭告警的总和
     */
    @JsonProperty("TotalAlerts")
    public int getTotalAlerts() {
        return getTotalNewAlerts() + getTotalClosedAlerts();
    }
}
