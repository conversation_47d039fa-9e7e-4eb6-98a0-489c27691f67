package com.ebon.energy.fms.mapper.fourth;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.AspNetUserClaimsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AspNetUserClaims Mapper 接口
 */
@Mapper
public interface AspNetUserClaimsMapper extends BaseMapper<AspNetUserClaimsDO> {

    /**
     * 根据声明类型查询声明值列表
     *
     * @param claimType 声明类型
     * @return 声明值列表
     */
    @Select("SELECT ClaimValue FROM AspNetUserClaims WHERE ClaimType = #{claimType}")
    List<String> selectClaimValuesByType(String claimType);
}
