package com.ebon.energy.fms.mapper.fourth;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.AspNetUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AspNetUsersMapper extends BaseMapper<AspNetUserDO> {

    @Select("select Id from AspNetUsers where Email=#{email}")
    String selectPortalUserId(@Param("email") String email);

}