package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.CacheDataDO;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import org.apache.ibatis.annotations.*;

@Mapper
public interface CacheDataMapper extends BaseMapper<CacheDataDO> {

    @Select("SELECT * FROM [dbo].[CacheData] WHERE keyValue = #{key}")
    CacheDataDO selectByKey(@Param("key") String key);

    @Update("MERGE INTO [dbo].[CacheData] AS target " +
            "USING (SELECT #{keyValue} AS KeyValue, #{dataValue} AS DataValue, #{expireAt} AS ExpireAt) AS source " +
            "ON (target.[KeyValue] = source.[KeyValue]) " +
            "WHEN MATCHED THEN " +
            "    UPDATE SET [DataValue] = source.[DataValue], [ExpireAt] = source.[ExpireAt] " +
            "WHEN NOT MATCHED THEN " +
            "    INSERT ([KeyValue], [DataValue], [ExpireAt]) " +
            "    VALUES (source.[KeyValue], source.[DataValue], source.[ExpireAt]);")
    void save(CacheDataDO cacheDataDO);

    @Delete("DELETE FROM [dbo].[CacheData] WHERE keyValue = #{key}")
    void deleteCache(@Param("key") String key);
}
