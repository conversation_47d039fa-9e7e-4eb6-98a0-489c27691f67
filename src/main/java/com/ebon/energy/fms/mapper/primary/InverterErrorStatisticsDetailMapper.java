package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsDetailDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface InverterErrorStatisticsDetailMapper extends BaseMapper<InverterErrorStatisticsDetailDO> {

    @Insert({
            "<script>",
            "INSERT INTO InverterErrorStatisticsDetail (StatisticsId, FirstTime, LastTime) ",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.statisticsId}, #{item.firstTime}, #{item.lastTime})",
            "</foreach>",
            "</script>"
    })
    int insertBatch(@Param("list") List<InverterErrorStatisticsDetailDO> list);

    @Update({
            "<script>",
            "MERGE INTO InverterErrorStatisticsDetail AS target",
            "USING (",
            "<foreach collection='list' item='item' separator=' UNION ALL '>",
            "(SELECT #{item.id} AS Id, ",
            "#{item.lastTime} AS LastTime)",
            "</foreach>",
            ") AS source (Id, LastTime)",
            "ON target.Id = source.Id",
            "WHEN MATCHED THEN",
            "UPDATE SET",
            "target.LastTime = source.LastTime",
            ";",
            "</script>"
    })
    int updateBatch(@Param("list") List<InverterErrorStatisticsDetailDO> list);

    @Select("<script>" +
            "SELECT * FROM InverterErrorStatisticsDetail " +
            " where StatisticsId in " +
            "<foreach item='item' collection='statisticsIds' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<InverterErrorStatisticsDetailDO> selectByStatisticsIds(@Param("statisticsIds") List<Integer> statisticsIds);

}
