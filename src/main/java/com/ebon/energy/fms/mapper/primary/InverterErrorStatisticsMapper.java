package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsDO;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsExtDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.mapping.StatementType;

import java.util.List;

@Mapper
public interface InverterErrorStatisticsMapper extends BaseMapper<InverterErrorStatisticsDO> {

    @Select({
            "<script>",
            "INSERT INTO InverterErrorStatistics (RedbackProductSn, [Date], ErrorCode, [Count]) ",
            "OUTPUT inserted.Id, inserted.RedbackProductSn, inserted.Date, inserted.ErrorCode, inserted.Count ",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "('${item.redbackProductSn}', '${item.date}', '${item.errorCode}', ${item.count})",
            "</foreach>",
            "</script>"
    })
    @Options(statementType = StatementType.STATEMENT)
    @Results({
            @Result(property = "id", column = "Id"),
            @Result(property = "redbackProductSn", column = "RedbackProductSn"),
            @Result(property = "date", column = "Date"),
            @Result(property = "errorCode", column = "ErrorCode"),
            @Result(property = "count", column = "Count"),
    })
    List<InverterErrorStatisticsDO> insertBatchWithOutput(@Param("list") List<InverterErrorStatisticsDO> list);

    @Update({
            "<script>",
            "MERGE INTO InverterErrorStatistics AS target",
            "USING (",
            "<foreach collection='list' item='item' separator=' UNION ALL '>",
            "(SELECT #{item.redbackProductSn} AS RedbackProductSn, ",
            "#{item.date} AS Date, ",
            "#{item.errorCode} AS ErrorCode, ",
            "#{item.count} AS Count)",
            "</foreach>",
            ") AS source (RedbackProductSn, Date, ErrorCode, Count)",
            "ON target.RedbackProductSn = source.RedbackProductSn",
            "AND target.Date = source.Date",
            "AND target.ErrorCode = source.ErrorCode",
            "WHEN MATCHED THEN",
            "UPDATE SET",
            "target.Count = source.Count",
            ";",
            "</script>"
    })
    int updateBatch(@Param("list") List<InverterErrorStatisticsExtDO> list);

    @Select("<script>" +
            "SELECT i.Id,i.RedbackProductSn,i.Date,i.ErrorCode,i.Count,d.Id AS lastDetailId,d.LastTime FROM InverterErrorStatistics i " +
            " LEFT JOIN InverterErrorStatisticsDetail d ON i.Id = d.StatisticsId " +
            " AND d.LastTime = (SELECT MAX(LastTime) FROM InverterErrorStatisticsDetail WHERE StatisticsId = i.Id) " +
            " where i.Date=#{Date} and i.RedbackProductSn in " +
            "<foreach item='item' collection='serialNumbers' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<InverterErrorStatisticsExtDO> selectByDate(@Param("Date") String Date, @Param("serialNumbers") List<String> serialNumbers);
}
