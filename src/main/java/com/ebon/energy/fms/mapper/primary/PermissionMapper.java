package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.PermissionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface PermissionMapper extends BaseMapper<PermissionDO> {

    @Select("select p.* from Permission p, RolePermission rp where p.Id=rp.PermissionId and rp.RoleId=#{roleId} and p.Status=1")
    List<PermissionDO> selectByRoleId(@Param("roleId") Integer roleId);

    @Select("select p.* from Permission p, RolePermission rp, FmsUserRole ur where p.Id=rp.PermissionId" +
            " and rp.RoleId=ur.RoleId and ur.UserId=#{userId} and p.Status=1")
    List<PermissionDO> selectByUserId(@Param("userId") Integer userId);
}
