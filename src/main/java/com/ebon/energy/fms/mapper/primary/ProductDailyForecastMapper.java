package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ProductDailyForecastDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.sql.Timestamp;
import java.util.List;

@Mapper
public interface ProductDailyForecastMapper extends BaseMapper<ProductDailyForecastDO> {
    
    @Insert({
            "<script>",
            "MERGE INTO ProductDailyForecast AS target",
            "USING (",
            "<foreach collection='list' item='item' separator='UNION ALL'>",
            "SELECT ",
            "#{item.redbackProductSn} AS RedbackProductSn,",
            "#{item.date} AS Date,",
            "#{item.generation} AS Generation",
            "</foreach>",
            ") AS source ON (target.RedbackProductSn = source.RedbackProductSn AND target.Date = source.Date)",
            "WHEN MATCHED THEN",
            "UPDATE SET target.Generation = source.Generation",
            "WHEN NOT MATCHED THEN",
            "INSERT (RedbackProductSn, Date, Generation)",
            "VALUES (source.RedbackProductSn, source.Date, source.Generation);",
            "</script>"
    })
    int saveBatch(@Param("list") List<ProductDailyForecastDO> list);

    @Select("select * from ProductDailyForecast where RedbackProductSn=#{redbackProductSn} and Date>=#{startTime} and Date<=#{endTime}")
    List<ProductDailyForecastDO> selectInRange(@Param("redbackProductSn") String redbackProductSn, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
