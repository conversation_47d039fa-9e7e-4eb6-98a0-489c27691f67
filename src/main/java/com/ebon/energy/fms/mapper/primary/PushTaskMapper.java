package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.entity.PushTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 推送任务Mapper接口
 */
@Mapper
public interface PushTaskMapper extends BaseMapper<PushTaskDO> {

    /**
     * 分页查询推送任务
     *
     * @param page 分页参数
     * @param os 操作系统过滤
     * @param status 状态过滤
     * @param keyword 关键词搜索（标题或内容）
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT * FROM PushTask " +
            "WHERE 1=1 " +
            "<if test='os != null and os != \"\"'>" +
            "  AND os = #{os} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "  AND status = #{status} " +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "  AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY created_at DESC" +
            "</script>")
    IPage<PushTaskDO> selectPageWithConditions(Page<PushTaskDO> page, 
                                               @Param("os") String os, 
                                               @Param("status") String status, 
                                               @Param("keyword") String keyword);

    /**
     * 查询待发送的推送任务
     *
     * @param currentTime 当前时间戳
     * @return 待发送任务列表
     */
    @Select("SELECT * FROM PushTask " +
            "WHERE status = 'PENDING' " +
            "AND push_time <= #{currentTime} " +
            "ORDER BY push_time ASC")
    List<PushTaskDO> selectPendingTasks(@Param("currentTime") Long currentTime);

    /**
     * 更新任务状态和重试次数
     *
     * @param id 任务ID
     * @param status 新状态
     * @param retryCount 重试次数
     * @param updatedAt 更新时间
     * @param updatedBy 更新人
     * @return 更新行数
     */
    @Update("UPDATE PushTask SET " +
            "status = #{status}, " +
            "retry_count = #{retryCount}, " +
            "updated_at = #{updatedAt}, " +
            "updated_by = #{updatedBy} " +
            "WHERE id = #{id}")
    int updateTaskStatus(@Param("id") Long id, 
                        @Param("status") String status, 
                        @Param("retryCount") Integer retryCount, 
                        @Param("updatedAt") Long updatedAt, 
                        @Param("updatedBy") String updatedBy);

    /**
     * 根据状态统计任务数量
     *
     * @param status 状态
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM PushTask WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 统计总任务数
     *
     * @return 总任务数
     */
    @Select("SELECT COUNT(*) FROM PushTask")
    Long countTotal();

    /**
     * 统计今日创建的任务数
     *
     * @param startTime 今日开始时间戳
     * @param endTime 今日结束时间戳
     * @return 今日创建任务数
     */
    @Select("SELECT COUNT(*) FROM PushTask WHERE created_at >= #{startTime} AND created_at < #{endTime}")
    Long countTodayCreated(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 统计今日发送成功的任务数
     *
     * @param startTime 今日开始时间戳
     * @param endTime 今日结束时间戳
     * @return 今日发送成功任务数
     */
    @Select("SELECT COUNT(*) FROM PushTask WHERE status = 'SENT' AND updated_at >= #{startTime} AND updated_at < #{endTime}")
    Long countTodaySent(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 计算平均重试次数
     *
     * @return 平均重试次数
     */
    @Select("SELECT AVG(CAST(retry_count AS FLOAT)) FROM PushTask WHERE status IN ('SENT', 'FAILED')")
    Double calculateAverageRetryCount();
}
