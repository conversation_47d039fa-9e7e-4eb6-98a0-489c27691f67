package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.FmsRoleDO;
import com.ebon.energy.fms.domain.vo.RoleDropDownVO;
import com.ebon.energy.fms.domain.vo.RoleVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface RoleMapper extends BaseMapper<FmsRoleDO> {

    @Select("<script>" +
            "SELECT r.Id, " +
            "   r.RoleName, " +
            "   r.Status, " +
            "   r.updatedAt, " +
            "   COUNT(ur.UserId) AS UserCount " +
            "FROM " +
            "   [dbo].[FmsRole] r " +
            "LEFT JOIN " +
            "   [dbo].[FmsUserRole] ur ON r.Id = ur.RoleId " +
            "<where>" +
            "   <if test='roleId != null '>" +
            "       AND r.Id = #{roleId} " +
            "   </if>" +
            "</where>" +
            "GROUP BY r.Id, r.<PERSON>, r.<PERSON>, r.updatedAt " +
            "ORDER BY r.updatedAt DESC " +
            "offset #{offset} rows " +
            "fetch next #{pageSize} rows only " +
            "</script>")
    List<RoleVO> selectAll(@Param("roleId") Integer roleId,
                           @Param("offset") Integer offset,
                           @Param("pageSize")Integer pageSize);

    @Select("<script>" +
            "SELECT count(r.Id) " +
            "FROM " +
            "   [dbo].[FmsRole] r " +
            "<where>" +
            "   <if test='roleId != null '>" +
            "       AND r.Id = #{roleId} " +
            "   </if>" +
            "</where>" +
            "</script>")
    Long countRoleList(@Param("roleId") Integer roleId);

    @Select("select * from FmsRole where RoleName = #{roleName}")
    FmsRoleDO findByRoleName(@Param("roleName") String roleName);

    @Select("select * from FmsRole where id = #{roleId}")
    FmsRoleDO findByRoleId(@Param("roleId") int roleId);

    @Insert("insert into FmsRole (RoleName, Status, DataPermission) values (#{roleName}, #{status}, #{dataPermission})")
    void addNewRole(@Param("roleName") String roleName, @Param("status") boolean status, @Param("dataPermission") Integer dataPermission);

    @Update("update FmsRole set RoleName = #{roleName}, Status = #{status}, DataPermission = #{dataPermission} where id = #{roleId}")
    void modifyRole(@Param("roleId") int roleId, @Param("roleName") String roleName, @Param("status")  boolean status, @Param("dataPermission") Integer dataPermission);

    @Select("select Id, RoleName, status from FmsRole order by id asc")
    List<RoleDropDownVO> selectDropdownList();
}
