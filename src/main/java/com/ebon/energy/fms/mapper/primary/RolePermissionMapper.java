package com.ebon.energy.fms.mapper.primary;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.RolePermissionDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermissionDO> {

    /**
     * 批量插入角色权限关联
     */
    @Insert({
            "<script>",
            "INSERT INTO RolePermission (RoleId, PermissionId, CreatedBy) ",
            "VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.roleId}, #{item.permissionId}, #{item.createdBy})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("list") List<RolePermissionDO> rolePermissions);

    /**
     * 批量删除角色权限
     */
    @Delete({
            "<script>",
            "DELETE FROM RolePermission ",
            "WHERE RoleId = #{roleId} ",
            "AND PermissionId IN ",
            "<foreach collection='permissionIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    int batchDeleteByRoleIdAndPermIds(
            @Param("roleId") Integer roleId,
            @Param("permissionIds") List<Integer> permissionIds
    );
    
}
