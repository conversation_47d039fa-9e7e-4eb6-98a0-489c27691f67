package com.ebon.energy.fms.mapper.second;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.FirmwareVersionsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FirmwareVersionsMapper extends BaseMapper<FirmwareVersionsDO> {

    @Select("SELECT * FROM FirmwareVersions WHERE CONCAT(DSPVersion, DSPVersion, ARMVersion)=#{version}")
    FirmwareVersionsDO selectByVersion(@Param("version") String version);

}
