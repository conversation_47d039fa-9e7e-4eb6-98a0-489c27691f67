package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.entity.SiteDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface AddressesMapper extends BaseMapper<AddressesDO> {

    @Select("SELECT * FROM Addresses addr inner join Site site on addr.id=site.AddressId " +
            "inner join RedbackProductInstallations installation on installation.SiteId = site.Id and installation.InstallationEndDate is null " +
            "WHERE installation.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
    AddressesDO selectSiteAddressBySn(@Param("serialNumber") String serialNumber);


    @Select({
            "<script>",
            "SELECT * FROM Addresses addr",
            "INNER JOIN Site site ON addr.id = site.AddressId",
            "INNER JOIN RedbackProductInstallations installation ON installation.SiteId = site.Id AND installation.InstallationEndDate IS NULL",
            "WHERE installation.RedbackProductSn IN",
            "<foreach item='sn' collection='sns' open='(' separator=',' close=')'>",
            "#{sn}",
            "</foreach>",
            "</script>"
    })
    List<AddressesDO> selectSiteAddressBySns(@Param("sns") List<String> sns);



    @Select({
            "<script>",
            "SELECT installation.RedbackProductSn as serialNumber, addr.TimeZoneId as timeZoneId",
            "FROM Addresses addr",
            "INNER JOIN Site site ON addr.id = site.AddressId",
            "INNER JOIN RedbackProductInstallations installation ON installation.SiteId = site.Id AND installation.InstallationEndDate IS NULL",
            "WHERE installation.RedbackProductSn IN",
            "<foreach item='sn' collection='sns' open='(' separator=',' close=')'>",
            "#{sn}",
            "</foreach>",
            "</script>"
    })
    List<Map<String, String>> selectTimeZoneMapBySns(@Param("sns") List<String> sns);

}