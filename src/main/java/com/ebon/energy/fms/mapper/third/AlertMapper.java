package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.alert.AlertActionModel;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警数据访问Mapper - 使用third数据源
 * 从C# Dapper查询转换为MyBatis
 */
@Mapper
public interface AlertMapper {

    /**
     * 获取所有告警信息
     */
    @Select("SELECT A.Id, A.<PERSON>ial<PERSON>ber, A.State, A.MonitorCondition, A.MonitorConditionType, " +
            "AD.Severity, AD.Description, A.CreatedOnUtc, A.LastModifiedOnUtc " +
            "FROM Alert A, AlertDefinition AD " +
            "WHERE A.MonitorConditionType = AD.Name")
    List<AlertModel> getAllAlerts();

    /**
     * 根据条件获取过滤后的告警数据
     */
    @Select({
        "<script>",
        "SELECT A.Id, A.<PERSON>, A.State, A.MonitorCondition, A.MonitorConditionType, ",
        "AD.Severity, AD.Description, A.CreatedOnUtc, A.LastModifiedOnUtc ",
        "FROM Alert A, AlertDefinition AD ",
        "WHERE A.MonitorConditionType = AD.Name ",
        
        // 监控条件过滤
        "<if test='monitoringConditions != null and monitoringConditions.length > 0'>",
        "AND A.MonitorCondition IN ",
        "<foreach collection='monitoringConditions' item='condition' open='(' separator=',' close=')'>",
        "#{condition}",
        "</foreach>",
        "</if>",
        
        // 严重级别过滤
        "<if test='severity != null and severity.length > 0'>",
        "AND AD.Severity IN ",
        "<foreach collection='severity' item='sev' open='(' separator=',' close=')'>",
        "#{sev}",
        "</foreach>",
        "</if>",
        
        // 状态过滤
        "<if test='states != null and states.length > 0'>",
        "AND A.State IN ",
        "<foreach collection='states' item='state' open='(' separator=',' close=')'>",
        "#{state}",
        "</foreach>",
        "</if>",
        
        "</script>"
    })
    List<AlertModel> getFilteredAlerts(
        @Param("monitoringConditions") String[] monitoringConditions,
        @Param("severity") String[] severity,
        @Param("states") String[] states
    );

    /**
     * 根据告警ID获取告警详情
     */
    @Select("SELECT A.Id, A.SerialNumber, A.State, A.MonitorCondition, A.MonitorConditionType, " +
            "AD.Severity, AD.Description, A.CreatedOnUtc, A.LastModifiedOnUtc " +
            "FROM Alert A, AlertDefinition AD " +
            "WHERE A.MonitorConditionType = AD.Name AND A.Id = #{alertId}")
    AlertModel getAlertById(@Param("alertId") String alertId);

    /**
     * 根据告警ID获取告警操作记录
     */
    @Select("SELECT Id, AlertId, Action, Result, Details, LastModifiedById, LastModifiedOnUtc, " +
            "CreatedById, CreatedOnUtc " +
            "FROM AlertAction " +
            "WHERE AlertId = #{alertId} " +
            "ORDER BY CreatedOnUtc DESC")
    List<AlertActionModel> getAlertActionsByAlertId(@Param("alertId") String alertId);

    /**
     * 根据设备序列号获取历史告警数据
     */
    @Select("SELECT A.Id, A.SerialNumber, A.State, A.MonitorCondition, A.MonitorConditionType, " +
            "AD.Severity, AD.Description, A.CreatedOnUtc, A.LastModifiedOnUtc " +
            "FROM Alert A, AlertDefinition AD " +
            "WHERE A.MonitorConditionType = AD.Name AND A.SerialNumber = #{serialNumber} " +
            "ORDER BY A.LastModifiedOnUtc DESC")
    List<AlertModel> getAlertHistoryBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 分页获取过滤后的告警数据
     */
    @Select({
        "<script>",
        "SELECT A.Id, A.SerialNumber, A.State, A.MonitorCondition, A.MonitorConditionType, ",
        "AD.Severity, AD.Description, A.CreatedOnUtc, A.LastModifiedOnUtc ",
        "FROM Alert A, AlertDefinition AD ",
        "WHERE A.MonitorConditionType = AD.Name ",

        // 时间过滤
        "<if test='dateRange != null'>",
        "AND A.LastModifiedOnUtc > #{dateRange}",
        "</if>",

        // 监控条件过滤
        "<if test='monitoringConditions != null and monitoringConditions.length > 0'>",
        "AND A.MonitorCondition IN ",
        "<foreach collection='monitoringConditions' item='condition' open='(' separator=',' close=')'>",
        "#{condition}",
        "</foreach>",
        "</if>",

        // 严重级别过滤
        "<if test='severity != null and severity.length > 0'>",
        "AND AD.Severity IN ",
        "<foreach collection='severity' item='sev' open='(' separator=',' close=')'>",
        "#{sev}",
        "</foreach>",
        "</if>",

        // 状态过滤
        "<if test='states != null and states.length > 0'>",
        "AND A.State IN ",
        "<foreach collection='states' item='state' open='(' separator=',' close=')'>",
        "#{state}",
        "</foreach>",
        "</if>",

        "ORDER BY A.CreatedOnUtc DESC ",
        "OFFSET #{offset} ROWS FETCH NEXT #{pageSize} ROWS ONLY",
        "</script>"
    })
    List<AlertModel> getFilteredAlertsWithPagination(
        @Param("dateRange") LocalDateTime dateRange,
        @Param("monitoringConditions") String[] monitoringConditions,
        @Param("severity") String[] severity,
        @Param("states") String[] states,
        @Param("pageSize") int pageSize,
        @Param("offset") int offset
    );

    /**
     * 统计过滤后的告警数据总数
     */
    @Select({
        "<script>",
        "SELECT COUNT(*) ",
        "FROM Alert A, AlertDefinition AD ",
        "WHERE A.MonitorConditionType = AD.Name ",

        // 时间过滤
        "<if test='dateRange != null'>",
        "AND A.LastModifiedOnUtc > #{dateRange}",
        "</if>",

        // 监控条件过滤
        "<if test='monitoringConditions != null and monitoringConditions.length > 0'>",
        "AND A.MonitorCondition IN ",
        "<foreach collection='monitoringConditions' item='condition' open='(' separator=',' close=')'>",
        "#{condition}",
        "</foreach>",
        "</if>",

        // 严重级别过滤
        "<if test='severity != null and severity.length > 0'>",
        "AND AD.Severity IN ",
        "<foreach collection='severity' item='sev' open='(' separator=',' close=')'>",
        "#{sev}",
        "</foreach>",
        "</if>",

        // 状态过滤
        "<if test='states != null and states.length > 0'>",
        "AND A.State IN ",
        "<foreach collection='states' item='state' open='(' separator=',' close=')'>",
        "#{state}",
        "</foreach>",
        "</if>",

        "</script>"
    })
    long countFilteredAlerts(
        @Param("dateRange") LocalDateTime dateRange,
        @Param("monitoringConditions") String[] monitoringConditions,
        @Param("severity") String[] severity,
        @Param("states") String[] states
    );
}
