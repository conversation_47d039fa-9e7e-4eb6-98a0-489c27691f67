package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ConfigurationsDO;
import com.ebon.energy.fms.domain.vo.product.control.ScheduleAuditDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ConfigurationsMapper extends BaseMapper<ConfigurationsDO> {



    /**
     * Get active schedule create audits for a specific serial number
     *
     * @param serialNumber The product serial number
     * @return List of schedule audit DTOs
     */
    @Select("SELECT " +
            "s.ScheduleId as scheduleId, " +
            "s.CreatedOnUtc as createdOnUtc, " +
            "s.CreatedChannel as createdChannel, " +
            "createdBy.Email as createdByEmail, " +
            "s.DeletedOnUtc as deletedOnUtc, " +
            "deletedBy.Email as deletedByEmail, " +
            "s.DeletedChannel as deletedChannel " +
            "FROM dbo.InverterSchedule s " +
            "LEFT JOIN AspNetUsers_External createdBy ON createdBy.Id = s.CreatedById " +
            "LEFT JOIN AspNetUsers_External deletedBy ON deletedBy.Id = s.DeletedById " +
            "WHERE s.serialNumber = #{serialNumber} " +
            "AND (s.StartAtUtc > CURRENT_TIMESTAMP OR s.DaysOfWeek is not null) " +
            "AND s.DeletedOnUtc IS NULL")
    List<ScheduleAuditDto> getActiveScheduleCreateAudits(@Param("serialNumber") String serialNumber);

    /**
     * Find configuration by Redback product serial number
     *
     * @param redbackProductSn The Redback product serial number
     * @return ConfigurationsDO object matching the serial number
     */
    @Select("SELECT * FROM Configurations WHERE RedbackProductSn = #{redbackProductSn}")
    ConfigurationsDO findByRedbackProductSn(@Param("redbackProductSn") String redbackProductSn);

    @Select("SELECT TOP 1 con.Configurations " +
            "  FROM dbo.Configurations con " +
            " WHERE con.RedbackProductSn = #{serialNumber} " +
            "   AND con.ConfigurationType = #{configurationType}")
    String selectConfigurationByType(@Param("serialNumber") String SerialNumber,@Param("configurationType") Integer configurationType);

}
