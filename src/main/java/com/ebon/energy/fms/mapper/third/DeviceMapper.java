package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.DeviceDO;
import com.ebon.energy.fms.domain.vo.product.DeviceIdInfoVo;
import com.ebon.energy.fms.domain.vo.product.DeviceSettingsDetailVo;
import com.ebon.energy.fms.domain.vo.product.control.DeviceSettingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DeviceMapper extends BaseMapper<DeviceDO> {

    @Select({"<script>",
            "select * from Device where ApplicationName=#{applicationName} and SerialNumber IN ",
            "<foreach item='item' collection='serialNumbers' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<DeviceDO> selectByApplicationNameAndSns(@Param("applicationName") String applicationName, @Param("serialNumbers") List<String> serialNumbers);

    /**
     * 根据设备序列号查询设备期望设置
     *
     * @param serialNumber 设备序列号
     * @return 设备期望设置JSON字符串
     */
    @Select("SELECT top 1 ds.DesiredDeviceSettings as desired , product.HardwareConfig as hardwareConfig, product.LatestSystemStatus as latestSystemStatus, ds.ReportedDeviceSettings as reportedDeviceSettings " +
            "FROM DeviceSettings ds " +
            "LEFT JOIN Device d ON ds.DeviceId = d.Id " +
            "LEFT JOIN  RedbackProducts product ON d.SerialNumber = product.RedbackProductSn " +
            "WHERE d.SerialNumber = #{serialNumber} " +
            "AND d.ApplicationName = 'Ross' " +
            "ORDER BY d.ModifiedDateUtc DESC "
            )
    DeviceSettingVo getDesiredDeviceSettingsBySerialNumber(@Param("serialNumber") String serialNumber);
    
    /**
     * 根据设备序列号查询设备ID信息
     *
     * @param serialNumber 设备序列号
     * @return 设备ID信息
     */
    @Select("SELECT TOP 1 d.Id as id, d.DeviceId as deviceId, d.CloudPlatformName as cloudPlatformName " +
            "FROM Device d " +
            "WHERE d.SerialNumber = #{serialNumber} " +
            "AND d.ApplicationName = 'Ross' " +
            "ORDER BY d.ModifiedDateUtc DESC "
          )
    DeviceIdInfoVo getDeviceIdInformationBySerialNumber(@Param("serialNumber") String serialNumber);
    
    /**
     * 根据设备ID查询设备表ID
     *
     * @param deviceId 设备ID
     * @return 设备表ID
     */
    @Select("SELECT TOP 1 d.Id " +
            "FROM Device d " +
            "WHERE d.DeviceId = #{deviceId} " +
            "AND d.ApplicationName = 'Ross' " +
            "ORDER BY d.ModifiedDateUtc DESC")
    String getIdOfDeviceTableByDeviceId(@Param("deviceId") String deviceId);



    @Select("SELECT \n" +
            "    d.ModifiedDateUtc as modifiedDateUtc,\n" +
            "    ds.ReportedDeviceSettings as reportedDeviceSettings,\n" +
            "    ds.DesiredDeviceSettings as desiredDeviceSettings,\n" +
            "    ds.DeviceSettingsIntent as deviceSettingsIntent,\n" +
            "    ds.ReportedLastUpdated as reportedLastUpdated,\n" +
            "    ds.DesiredDeviceSettingsPatch as desiredDeviceSettingsPatch,\n" +
            "    ds.DesiredVersion as desiredVersion,\n" +
            "    product.LatestSystemStatus as latestSystemStatus,\n" +
            "    product.HardwareConfig as hardwareConfig,\n" +
            "    product.RedbackProductSn as redbackProductSn,\n" +
            "    o.MinOnGridSoC0to100 as minOnGridSoC0to100,\n" +
            "    o.MaxOnGridSoC0to100 as maxOnGridSoC0to100,\n" +
            "    o.MinOffGridSoC0to100 as minOffGridSoC0to100,\n" +
            "    o.MaxOffGridSoC0to100 as maxOffGridSoC0to100\n" +
            "FROM \n" +
            "    dbo.Device d\n" +
            "INNER JOIN \n" +
            "    dbo.DeviceSettings ds ON d.Id = ds.DeviceId\n" +
            "INNER JOIN \n" +
            "    dbo.RedbackProducts product ON d.SerialNumber = product.RedbackProductSn\n" +
            "LEFT JOIN \n" +
            "    dbo.ExplicitSettings o ON product.RedbackProductSn = o.SerialNumber\n" +
            "WHERE \n" +
            "    d.SerialNumber = #{serialNumber} \n" +
            "    AND d.ApplicationName = 'Ross'\n" +
            "ORDER BY \n" +
            "    d.ModifiedDateUtc DESC")
    DeviceSettingsDetailVo getDeviceSettingsDtoQueryNoAuth(@Param("serialNumber") String serialNumber);
}
