package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.installer.FleetHealthSummaryViewModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FleetHealthSummaryMapper {

    @Select("<script>" +
            "        DECLARE @UserType INT;\n" +
            "        SELECT @UserType = UserType\n" +
            "        FROM   RedbackUsers\n" +
            "        WHERE  ID = #{loggedInUser};\n" +
            "\n" +
            "        SELECT  InverterStatus,\n" +
            "                COUNT(*) AS InverterCount\n" +
            "        FROM   (\n" +
            "            SELECT\n" +
            "                CASE\n" +
            "                    WHEN IsInWarranty = 1 THEN 'Warranty'\n" +
            "                    WHEN OffComms     = 1 THEN 'Permanently Offline'\n" +
            "                    WHEN [Address] IS NULL OR [Address] = '' OR TRIM(u.Address) = ',' THEN 'Unregistered'\n" +
            "                    WHEN [Address] IS NOT NULL AND OffComms &lt;&gt; 1 AND SystemStatusTimeStamp_Brisbane IS NULL\n" +
            "                         THEN 'PendingInstall'\n" +
            "                    WHEN MinutesSinceLastTelemetry IS NULL OR MinutesSinceLastTelemetry &gt; 10\n" +
            "                         THEN 'Offline'\n" +
            "                    ELSE\n" +
            "                        CASE\n" +
            "                            WHEN @UserType = 0      AND IncidentsForHomeUser    &gt; 0 THEN 'RequiresAttention'\n" +
            "                            WHEN @UserType IN (1,2) AND UnhealthyAtUtcInstaller &gt; 0 THEN 'RequiresAttention'\n" +
            "                            WHEN @UserType IN (3,4) AND UnhealthyAtUtcRb        &gt; 0 THEN 'RequiresAttention'\n" +
            "                            ELSE 'Healthy'\n" +
            "                        END\n" +
            "                END AS InverterStatus\n" +
            "            FROM (\n" +
            "                SELECT\n" +
            "                    CASE WHEN a.AddressLineOne IS NULL THEN '' ELSE a.AddressLineOne END    AS [Address],\n" +
            "                    CASE WHEN details.Value = 'True' THEN 1 ELSE 0 END                      AS OffComms,\n" +
            "                    p.LastSystemStatusReceived AT TIME ZONE 'UTC'\n" +
            "                                             AT TIME ZONE 'E. Australia Standard Time'      AS SystemStatusTimeStamp_Brisbane,\n" +
            "                    mt.LastTelemetryUtc,\n" +
            "                    mt.MinutesSinceLastTelemetry,\n" +
            "                    CASE WHEN mt.MinutesSinceLastTelemetry &lt;= 10 THEN 1 ELSE 0 END        AS [Online],\n" +
            "                    p.IsInWarranty,\n" +
            "                    mt.IncidentsForRb,\n" +
            "                    mt.IncidentsForInstaller,\n" +
            "                    mt.IncidentsForHomeUser,\n" +
            "                    mt.UnhealthyAtUtcRb,\n" +
            "                    mt.UnhealthyAtUtcInstaller,\n" +
            "                    mt.UnhealthyAtUtcHomeUser,\n" +
            "                    CASE\n" +
            "                        WHEN @UserType = 0      THEN mt.UnhealthyAtUtcHomeUser\n" +
            "                        WHEN @UserType IN (1,2) THEN mt.UnhealthyAtUtcInstaller\n" +
            "                        WHEN @UserType IN (3,4) THEN mt.UnhealthyAtUtcRb\n" +
            "                        ELSE mt.UnhealthyAtUtcHomeUser\n" +
            "                    END AS UnhealthyAtUtc\n" +
            "                FROM  dbo.RedbackProducts p  WITH(NOLOCK)\n" +
            "                INNER JOIN dbo.AllowedSerialNumbers(#{loggedInUser}) asn\n" +
            "                       ON  asn.SerialNumber = p.RedbackProductSn\n" +
            "                LEFT JOIN dbo.RedbackProductInstallations i WITH(NOLOCK)\n" +
            "                       ON  i.RedbackProductSn   = p.RedbackProductSn\n" +
            "                      AND i.InstallationEndDate IS NULL\n" +
            "                LEFT JOIN dbo.MetricsTelemetry mt WITH(NOLOCK)\n" +
            "                       ON  mt.SerialNumber      = p.RedbackProductSn\n" +
            "                LEFT JOIN dbo.Addresses a WITH(NOLOCK)\n" +
            "                       ON  a.Id                = i.AddressId\n" +
            "                LEFT JOIN dbo.RedbackProductInstallationDetails details\n" +
            "                       ON  details.RedbackProductInstallationId = i.Id\n" +
            "                      AND details.Name = 'IsSupposedToHaveInternetConnection'\n" +
            "            ) u\n" +
            "        ) f\n" +
            "        GROUP BY InverterStatus"+
            "</script>")
    List<FleetHealthSummaryViewModel> getFleetHealthSummary(@Param("loggedInUser") String loggedInUser);
}
