package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.HardwareFamilyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface HardwareFamilyMapper extends BaseMapper<HardwareFamilyDO> {

    @Select("SELECT " +
            "    Id, " +
            "    Name, " +
            "    ShortName, " +
            "    SystemType, " +
            "    DisplayInLargeInstaller, " +
            "    MarketingDisplayName, " +
            "    MarketingShortCode " +
            "FROM HardwareFamily " +
            "WHERE DisplayInLargeInstaller = 1")
    List<HardwareFamilyDO> selectDisplayInLargeInstaller();
}
