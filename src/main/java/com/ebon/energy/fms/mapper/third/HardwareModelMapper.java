package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.HardwareModelAndFamilyDO;
import com.ebon.energy.fms.domain.entity.HardwareModelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface HardwareModelMapper extends BaseMapper<HardwareModelDO> {

    @Select("SELECT " +
            "    hwm.Id,  " +
            "    hwm.HardwareFamilyId, " +
            "    hwm.[Name], " +
            "    hwm.DisplayName, " +
            "    hwm.IsGridTie, " +
            "    hwf.[Name] AS HardwareFamilyName, " +
            "    hwf.DisplayInLargeInstaller, " +
            "    hwf.MarketingDisplayName, " +
            "    hwf.MarketingShortCode " +
            "FROM dbo.HardwareModel hwm " +
            "JOIN dbo.HardwareFamily hwf " +
            "  ON hwf.Id = hwm.HardwareFamilyId " +
            "ORDER BY Id ")
    List<HardwareModelAndFamilyDO> selectWithFamily();

    @Select({"<script>",
            "SELECT * FROM dbo.HardwareModel where Id in ",
            "<foreach item='id' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    List<HardwareModelDO> selectByIds(@Param("ids") List<Integer> ids);

    @Select({"<script>",
            "SELECT * FROM dbo.HardwareModel where Name in ",
            "<foreach item='name' collection='names' open='(' separator=',' close=')'>",
            "#{name}",
            "</foreach>",
            "</script>"})
    List<HardwareModelDO> selectByNames(@Param("names") List<String> names);
}
