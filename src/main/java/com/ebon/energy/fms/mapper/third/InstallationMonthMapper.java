package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.installer.InstallationMonthViewModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InstallationMonthMapper {

    @Select("<script>" +
            "SELECT " +
            "    DATEFROMPARTS(YEAR(rpi.InstallationStartDateUTC), MONTH(rpi.InstallationStartDateUTC), 1) AS InstallationMonth, " +
            "    1 AS Installed, " +
            "    CONCAT( " +
            "        COALESCE(hf.MarketingDisplayName, hf.[Name]),  " +
            "        ' (', " +
            "        COALESCE(hf.MarketingShortCode, hf.ShortName),  " +
            "        ')' " +
            "    ) AS Model, " +
            "    hf.ShortName AS ModelShortCode, " +
            "    COALESCE(hf.MarketingShortCode, hf.ShortName) AS DisplayShortCode " +
            "FROM dbo.RedbackProducts i WITH (NOLOCK) " +
            "" +
            "CROSS APPLY ( " +
            "    SELECT TOP 1 rpi2.Id,  " +
            "        rpi2.InstallationEndDate, " +
            "        CONVERT(DATETIME, CONVERT(DATETIMEOFFSET, rpi2.InstallationStartDate) AT TIME ZONE 'E. Australia Standard Time') AS InstallationStartDateUTC " +
            "    FROM RedbackProductInstallations rpi2 WITH (NOLOCK) " +
            "    WHERE rpi2.RedbackProductSn = i.RedbackProductSn " +
            "    ORDER BY rpi2.InstallationStartDate DESC " +
            ") rpi " +
            "" +
            "CROSS APPLY ( " +
            "    SELECT TOP 1 c2.Id " +
            "    FROM dbo.Configurations c2 WITH (NOLOCK) " +
            "    WHERE c2.RedbackProductSn = i.RedbackProductSn " +
            "        AND ConfigurationType = 0 " +
            "    ORDER BY ModifiedDateTime DESC " +
            ") c " +
            "" +
            "JOIN dbo.AllowedSerialNumbers(#{userId}) asn  ON asn.SerialNumber = i.RedbackProductSn " +
            "" +
            "LEFT JOIN dbo.Device d WITH (NOLOCK) ON d.SerialNumber = i.RedbackProductSn AND d.ApplicationName = 'Ross' " +
            "" +
            "LEFT JOIN dbo.DeviceSettings ds WITH (NOLOCK) ON ds.DeviceId = d.Id " +
            "" +
            "LEFT JOIN HardwareModel hm WITH (NOLOCK) ON hm.Name = ISNULL(JSON_VALUE(i.LatestSystemStatus, '$.Inverter.ModelName'), 'Unknown') " +
            "" +
            "JOIN HardwareFamily hf  WITH (NOLOCK) ON hf.Id = hm.HardwareFamilyId " +
            "WHERE " +
            "    i.LastSystemStatusReceived IS NOT NULL " +
            "    AND rpi.InstallationStartDateUTC IS NOT NULL " +
            "    AND rpi.InstallationEndDate IS NULL " +
            "    AND hf.DisplayInLargeInstaller = 1 " +
            "    AND ISJSON(i.LatestSystemStatus) = 1" +
            "</script>")
    List<InstallationMonthViewModel> selectInstallationMonthData(@Param("userId") String userId);
}
