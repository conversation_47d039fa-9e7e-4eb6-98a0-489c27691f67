package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.product.control.ProductInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ProductInstallationMapper extends BaseMapper<RedbackProductInstallationDO> {

    @Select("SELECT ind.[Value] " +
            "  FROM RedbackProductInstallations ins " +
            "  JOIN RedbackProductInstallationDetails ind " +
            "    ON ind.RedbackProductInstallationId = ins.Id " +
            " WHERE ins.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR} " +
            "   AND ins.InstallationEndDate IS NULL " +
            "   AND (ind.Name = 'PVSize1' OR ind.Name = 'PVSize2' OR ind.Name = 'PVSize3')")
    List<String> selectPVSize(@Param("serialNumber") String serialNumber);

    @Update("update RedbackProductInstallations" +
            " set PhaseRole = #{phaseRole}" +
            " where RedbackProductSn=#{serialNumber} and InstallationEndDate is null")
    int updatePhaseRole(@Param("serialNumber") String serialNumber, @Param("phaseRole") String phaseRole);

    @Update("declare @MainProduct varchar(100) = '' " +
            "select @MainProduct = s.OriginalPrimarySN from dbo.Site s where s.SystemId = #{siteId} " +
            "exec dbo.MergeProductsToOneSite @MainProduct, #{serialNumber}")
    void mergeProductToSite(@Param("siteId") String siteId, @Param("serialNumber") String serialNumber);
    
    @Select("select count(i.RedbackProductSn) from dbo.Site s  " +
            " join dbo.RedbackProductInstallations i on i.SiteId = s.Id " +
            " where s.SystemId = #{siteId} " +
            " and i.InstallationEndDate is null " +
            " and i.RedbackProductSn = #{serialNumber}")
    int countBySiteIdAndSn(@Param("siteId") String siteId, @Param("serialNumber") String serialNumber);

    @Select("select * from dbo.RedbackProductInstallations where RedbackProductSn = #{serialNumber} and InstallationEndDate is null")
    RedbackProductInstallationDO selectBySn(@Param("serialNumber") String serialNumber);
    
}
