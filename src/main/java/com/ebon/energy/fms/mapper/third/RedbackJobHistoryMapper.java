package com.ebon.energy.fms.mapper.third;

import com.ebon.energy.fms.domain.vo.charts.RedbackJobHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RedbackJobHistoryMapper {

    /**
     * 根据作业名称获取最新的作业历史记录
     *
     * @param jobName 作业名称
     * @return 作业历史记录
     */
    @Select("SELECT JobName, LastRunUtc, JobSettings " +
            "FROM dbo.RedbackJobHistory " +
            "WHERE JobName = #{jobName} " +
            "ORDER BY LastRunUtc DESC")
    RedbackJobHistory getLatestJobHistoryByJobName(@Param("jobName") String jobName);

    /**
     * 根据作业名称列表获取作业历史记录
     *
     * @param jobNames 作业名称列表
     * @return 作业历史记录列表
     */
    @Select("<script>" +
            "SELECT JobName, LastRunUtc, JobSettings " +
            "FROM dbo.RedbackJobHistory " +
            "WHERE JobName IN " +
            "<foreach collection='jobNames' item='jobName' open='(' separator=',' close=')'>" +
            "#{jobName}" +
            "</foreach> " +
            "ORDER BY JobName, LastRunUtc DESC" +
            "</script>")
    List<RedbackJobHistory> getJobHistoriesByJobNames(@Param("jobNames") List<String> jobNames);
}
