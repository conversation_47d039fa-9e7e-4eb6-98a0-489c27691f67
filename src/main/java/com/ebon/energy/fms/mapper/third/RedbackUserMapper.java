package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.RedbackUserDO;
import com.ebon.energy.fms.domain.vo.InstallerCompanyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface RedbackUserMapper extends BaseMapper<RedbackUserDO> {

    @Select("SELECT a.Id,cn.ClaimValue as companyName,ua.AddressLineOne,ua.AddressLineTwo,ua.Suburb,ua.State,ua.Country,ua.PostCode,ua.Latitude,ua.Longitude,mp.ClaimValue as Phone,b.Email" +
            ",(select count(DISTINCT RedbackProductSn) from RedbackProductInstallations where InstallationInstallerCompany=a.id and InstallationEndDate is null) as InstalledCount " +
            " FROM [dbo].[RedbackUsers] a join AspNetUsers_External b on a.Id=b.Id and a.UserType=2 " +
            " left join dbo.AspNetUserClaims_External cn on cn.userId = a.Id and cn.ClaimType = 'CompanyName' " +
            " left join dbo.AspNetUserClaims_External mp on mp.userId = a.Id and mp.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone' " +
            " left join UserAddresses_External ua on ua.Id=b.AddressId")
    List<InstallerCompanyVO> selectInstallerCompanys();

}