package com.ebon.energy.fms.remote;

import cn.jiguang.sdk.api.PushApi;
import cn.jiguang.sdk.bean.push.PushSendParam;
import cn.jiguang.sdk.bean.push.PushSendResult;
import cn.jiguang.sdk.bean.push.audience.Audience;
import cn.jiguang.sdk.bean.push.callback.Callback;
import cn.jiguang.sdk.bean.push.message.notification.NotificationMessage;
import cn.jiguang.sdk.bean.push.options.Options;
import cn.jiguang.sdk.constants.ApiConstants;
import cn.jiguang.sdk.enums.platform.Platform;
import cn.jiguang.sdk.exception.ApiErrorException;
import com.ebon.energy.fms.config.JiGuangTConfig;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.okhttp.OkHttpClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class JiGuangRemoteService {

    private final JiGuangTConfig jiGuangTConfig;

    private static final okhttp3.OkHttpClient okHttpClient = new okhttp3.OkHttpClient().newBuilder().build();

//    @PostConstruct
//    public void init() {
//        // 保留示例代码到init方法
//        pushExample();
//    }

    /**
     * 推送消息API
     *
     * @param pushSendParam 推送参数
     * @return 推送结果
     * @throws BizException 业务异常
     */
    public PushSendResult pushMessage(PushSendParam pushSendParam) {
        log.info("开始推送消息，参数: {}", pushSendParam);

        try {
            // 参数校验
            validatePushParam(pushSendParam);

            // 创建推送API客户端
            PushApi pushApi = createPushApi();

            // 发送推送
            log.debug("调用极光推送API发送消息");
            PushSendResult result = pushApi.send(pushSendParam);

            log.info("推送消息成功，结果: {}", result);
            return result;

        } catch (ApiErrorException e) {
            log.error("极光推送API调用失败, 错误信息: {}", e.getApiError(), e);
            throw convertApiErrorException(e);
        } catch (Exception e) {
            log.error("推送消息时发生未知异常", e);
            throw new BizException(CommonErrorCodeEnum.PUSH_API_ERROR, "push message error: " + e.getMessage());
        }
    }

    /**
     * 创建推送API客户端
     */
    private PushApi createPushApi() {
        log.debug("创建极光推送API客户端");
        return new PushApi.Builder()
                .setClient(new OkHttpClient(okHttpClient))
                .setOptions(new Request.Options(10, TimeUnit.SECONDS, 10, TimeUnit.SECONDS, false))
                .setRetryer(new Retryer.Default(10, 10, 10))
                .setLoggerLevel(Logger.Level.FULL)
                .setAppKey(jiGuangTConfig.getAppKey())
                .setMasterSecret(jiGuangTConfig.getMasterSecret())
                .build();
    }

    /**
     * 校验推送参数
     */
    private void validatePushParam(PushSendParam pushSendParam) {
        if (pushSendParam == null) {
            throw new BizException("push param is null");
        }

        if (pushSendParam.getPlatform() == null) {
            throw new BizException("push platform is null");
        }

        if (pushSendParam.getAudience() == null) {
            throw new BizException( "push audience is null");
        }

        log.debug("推送参数校验通过");
    }

    /**
     * 转换ApiErrorException为平台异常
     */
    private BizException convertApiErrorException(ApiErrorException e) {
        return new BizException(CommonErrorCodeEnum.PUSH_API_ERROR, "send push error: [" + e.getMessage() + "]" );
    }

    /**
     * 推送示例代码（保留在init方法中）
     */
    private void pushExample() {
        try {
            log.info("执行推送示例代码");

            PushApi pushApi = createPushApi();
            PushSendParam param = new PushSendParam();
            // 通知内容
            NotificationMessage.Android android = new NotificationMessage.Android();
            android.setAlert("this is android alert");
            android.setTitle("this is android title");

            NotificationMessage.IOS iOS = new NotificationMessage.IOS();
            Map<String, String> iOSAlert = new HashMap<>();
            iOSAlert.put("title", "this is iOS title");
            iOSAlert.put("subtitle", "this is iOS subtitle");
            iOS.setAlert(iOSAlert);

            Map<String, Object> extrasMap = new HashMap<>();
            Map<String, Object> extrasParamMap = new HashMap<>();
            extrasParamMap.put("key1", "value1");
            extrasParamMap.put("key2", "value2");
            extrasMap.put("params", extrasParamMap);
            android.setExtras(extrasMap);
            iOS.setExtras(extrasMap);

            NotificationMessage notificationMessage = new NotificationMessage();
            notificationMessage.setAlert("this is alert");
            notificationMessage.setAndroid(android);
            notificationMessage.setIos(iOS);
            param.setNotification(notificationMessage);

            // 目标人群
            Audience audience = new Audience();
            audience.setRegistrationIdList(Arrays.asList("1507bfd3f6e0c048d79"));
            // 指定目标
             param.setAudience(audience);

            // 或者发送所有人
//            param.setAudience(ApiConstants.Audience.ALL);

            // 指定平台
            param.setPlatform(Arrays.asList(Platform.android, Platform.ios));
            // 或者发送所有平台
            // param.setPlatform(ApiConstants.Platform.ALL);

            // 短信补充
            // param.setSmsMessage();

            // 回调
            // param.setCallback();

            Map<String, Object> callbackParams = new HashMap<>();
            callbackParams.put("callbackKey", "callbackValue");
            Callback callback = new Callback();
            callback.setParams(callbackParams);
            param.setCallback(callback);
            pushMessage(param);
        } catch (Exception e) {
            log.warn("推送示例执行失败，Exception: {}", e.getMessage());
        }
    }
}
