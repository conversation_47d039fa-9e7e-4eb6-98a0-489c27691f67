package com.ebon.energy.fms.repository;

import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.domain.vo.alert.AlertActionModel;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警数据访问接口
 * 从C# ProductRepository转换而来
 */
public interface AlertRepository {

    /**
     * 获取所有告警信息
     * 
     * @return 告警列表
     */
    List<AlertModel> getAllAlerts();

    /**
     * 根据条件获取过滤后的告警数据
     * 
     * @param monitoringConditions 监控条件数组
     * @param severity 严重级别数组
     * @param states 状态数组
     * @return 过滤后的告警列表
     */
    List<AlertModel> getFilteredAlerts(String[] monitoringConditions, String[] severity, String[] states);

    /**
     * 根据告警ID获取告警详情
     * 
     * @param alertId 告警ID
     * @return 告警详情信息
     */
    AlertDetails getAlertDetailsById(String alertId);

    /**
     * 根据告警ID获取告警基本信息
     * 
     * @param alertId 告警ID
     * @return 告警基本信息
     */
    AlertModel getAlertById(String alertId);

    /**
     * 根据告警ID获取告警操作记录
     * 
     * @param alertId 告警ID
     * @return 告警操作记录列表
     */
    List<AlertActionModel> getAlertActionsByAlertId(String alertId);

    /**
     * 根据设备序列号获取历史告警数据
     *
     * @param serialNumber 设备序列号
     * @return 历史告警列表
     */
    List<AlertModel> getAlertHistoryBySerialNumber(String serialNumber);

    /**
     * 分页获取过滤后的告警数据
     *
     * @param dateRange 时间范围过滤
     * @param monitoringConditions 监控条件数组
     * @param severity 严重级别数组
     * @param states 状态数组
     * @param page 当前页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页告警数据
     */
    PageResult<AlertModel> getFilteredAlertsWithPagination(
        LocalDateTime dateRange,
        String[] monitoringConditions,
        String[] severity,
        String[] states,
        int page,
        int pageSize
    );
}
