package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.domain.vo.alert.AlertActionModel;
import com.ebon.energy.fms.domain.vo.alert.AlertDetails;
import com.ebon.energy.fms.domain.vo.alert.AlertModel;
import com.ebon.energy.fms.mapper.third.AlertMapper;
import com.ebon.energy.fms.repository.AlertRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警数据访问实现类
 * 从C# ProductRepository转换而来
 */
@Slf4j
@Repository
public class AlertRepositoryImpl implements AlertRepository {

    @Resource
    private AlertMapper alertMapper;

    @Override
    public List<AlertModel> getAllAlerts() {
        try {
            return alertMapper.getAllAlerts();
        } catch (Exception e) {
            log.error("获取所有告警信息失败", e);
            throw new RuntimeException("获取所有告警信息失败", e);
        }
    }

    @Override
    public List<AlertModel> getFilteredAlerts(String[] monitoringConditions, String[] severity, String[] states) {
        try {
            // 过滤空值和空字符串
            String[] filteredMonitoringConditions = filterEmptyStrings(monitoringConditions);
            String[] filteredSeverity = filterEmptyStrings(severity);
            String[] filteredStates = filterEmptyStrings(states);
            
            return alertMapper.getFilteredAlerts(
                filteredMonitoringConditions.length > 0 ? filteredMonitoringConditions : null,
                filteredSeverity.length > 0 ? filteredSeverity : null,
                filteredStates.length > 0 ? filteredStates : null
            );
        } catch (Exception e) {
            log.error("获取过滤后的告警数据失败", e);
            throw new RuntimeException("获取过滤后的告警数据失败", e);
        }
    }

    @Override
    public AlertDetails getAlertDetailsById(String alertId) {
        try {
            // 获取告警基本信息
            AlertModel alertDetail = alertMapper.getAlertById(alertId);
            
            if (alertDetail == null) {
                return null;
            }
            
            // 获取告警操作记录
            List<AlertActionModel> actionDetails = alertMapper.getAlertActionsByAlertId(alertId);
            
            // 获取历史告警数据
            List<AlertModel> historyData = alertMapper.getAlertHistoryBySerialNumber(alertDetail.getSerialNumber());
            
            // 构建告警详情对象
            AlertDetails alertDetails = new AlertDetails();
            alertDetails.setDetail(alertDetail);
            alertDetails.setActions(actionDetails);
            alertDetails.setHistoryData(historyData);
            
            return alertDetails;
            
        } catch (Exception e) {
            log.error("获取告警详情失败, alertId: {}", alertId, e);
            throw new RuntimeException("获取告警详情失败", e);
        }
    }

    @Override
    public AlertModel getAlertById(String alertId) {
        try {
            return alertMapper.getAlertById(alertId);
        } catch (Exception e) {
            log.error("根据ID获取告警信息失败, alertId: {}", alertId, e);
            throw new RuntimeException("根据ID获取告警信息失败", e);
        }
    }

    @Override
    public List<AlertActionModel> getAlertActionsByAlertId(String alertId) {
        try {
            return alertMapper.getAlertActionsByAlertId(alertId);
        } catch (Exception e) {
            log.error("获取告警操作记录失败, alertId: {}", alertId, e);
            throw new RuntimeException("获取告警操作记录失败", e);
        }
    }

    @Override
    public List<AlertModel> getAlertHistoryBySerialNumber(String serialNumber) {
        try {
            return alertMapper.getAlertHistoryBySerialNumber(serialNumber);
        } catch (Exception e) {
            log.error("获取历史告警数据失败, serialNumber: {}", serialNumber, e);
            throw new RuntimeException("获取历史告警数据失败", e);
        }
    }

    /**
     * 过滤空字符串和null值
     */
    private String[] filterEmptyStrings(String[] array) {
        if (array == null) {
            return new String[0];
        }
        
        return java.util.Arrays.stream(array)
            .filter(s -> s != null && !s.trim().isEmpty())
            .toArray(String[]::new);
    }
}
