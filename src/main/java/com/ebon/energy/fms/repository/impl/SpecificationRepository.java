package com.ebon.energy.fms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.HardwareFamilyEnum;
import com.ebon.energy.fms.common.enums.UniversalSettingSource;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.vo.DeviceSettingsIntentVO;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DesiredAndReported;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.mapper.third.DeviceInfoAndSettingsMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Slf4j
@Repository
@RequiredArgsConstructor
public class SpecificationRepository {
    private final ObjectMapper objectMapper;

    private final DeviceInfoAndSettingsMapper deviceInfoAndSettingsMapper;

    public InstallationSpecification getInstallationSpecAsync(String serialNumber) {
        var deviceSettings = getDeviceInfoAndSettings(null, serialNumber, null);
        var hardAndFirm = SpecificationFactory.get(deviceSettings.getIdentityCard());
        boolean mustHaveGrid = hardAndFirm != null && Boolean.TRUE.equals(hardAndFirm.isGridTieInverter());

        boolean measuringThirdPartyInverter = false;
        ICommonSettingsReader reader = SettingsReaderProvider.tryGet(deviceSettings);
        if (reader != null) {
            ACCoupledSettingsDto acCoupledSettings = reader.getACCoupledSettings(UniversalSettingSource.DESIRED);
            if (acCoupledSettings != null && acCoupledSettings.getIsACCoupledEnabled() != null) {
                measuringThirdPartyInverter = acCoupledSettings.getIsACCoupledEnabled();
            }
        }

        boolean hasIncorrectLoadandPvDailyCacheValues =
                getHasIncorrectLoadAndPvDailyCacheValues(hardAndFirm.getHardwareFamily(), measuringThirdPartyInverter);

        return new InstallationSpecification(
                hardAndFirm,
                hasIncorrectLoadandPvDailyCacheValues,
                measuringThirdPartyInverter,
                mustHaveGrid
        );
    }

    @SneakyThrows
    public DeviceInfoAndSettings getDeviceInfoAndSettings(
            String userId,
            String serialNumber,
            LocalDateTime asOf) {

        InternalDeviceInfoAndSettings data = deviceInfoAndSettingsMapper.getDeviceInfoAndSettings(userId, serialNumber, asOf);

        if (data == null) {
            data = new InternalDeviceInfoAndSettings();
//            throw new BizException("Data not found for '" + serialNumber + "'");
        }

        if (data.getFirmwareVersion() == null || data.getModelName() == null) {
            // 注册页面会进入这里，所以不能抛出异常
        }
        log.info("Getting device info and settings for serial number: {} - {}", serialNumber,data.getReported());
        RossDesiredSettings settings = StringUtils.isNotBlank(data.getDesired()) ? objectMapper.readValue(data.getDesired(), RossDesiredSettings.class) : null;
        RossReportedSettings reported = StringUtils.isNotBlank(data.getReported()) ? objectMapper.readValue(data.getReported(), RossReportedSettings.class) : null;
        DesiredAndReported desiredAndReported = new DesiredAndReported(settings, reported);

        DeviceSettingsIntentVO intent = null;
        if (data.getIntent() != null && !data.getIntent().isEmpty()) {
            intent = JSONObject.parseObject(data.getIntent(), DeviceSettingsIntentVO.class);
        }

        InverterIdentityCard identityCard = new InverterIdentityCard(
                serialNumber,
                data.getModelName(),
                data.getFirmwareVersion(),
                //todo check parse
                data.getSoftwareVersion() != null ? new RossVersion(Version.parse(data.getSoftwareVersion())) : null,
                data.getHardwareConfig()
        );

        return new DeviceInfoAndSettings(
                desiredAndReported.getReported(),
                desiredAndReported.getDesired(),
                intent,
                identityCard,
                data.asExplicitSettings()
        );
    }

    public static boolean getHasIncorrectLoadAndPvDailyCacheValues(
            HardwareFamilyEnum family,
            boolean isInAcCoupledMode) {
        return family == HardwareFamilyEnum.Inverter_Goodwe_ES && isInAcCoupledMode;
    }

}

