package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.domain.vo.site.SolarAndBackupPairVO;
import com.ebon.energy.fms.repository.TelemetryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.Instant;

@Slf4j
@Repository
public class TelemetryRepositoryImpl implements TelemetryRepository {
    
    @Override
    public SolarAndBackupPairVO getDeviceAverageBackupAndSolarPowerBetween(String serialNumber, Instant startUtc, Instant endUtc) {
        //todo 实现table storage的查询
        return null;
    }
}
