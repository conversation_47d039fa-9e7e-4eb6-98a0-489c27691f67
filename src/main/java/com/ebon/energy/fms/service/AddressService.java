package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.mapper.third.AddressesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class AddressService {
    
    @Resource
    private AddressesMapper addressesMapper;

    public AddressesDO getById(Integer id) {
        LambdaQueryWrapper<AddressesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AddressesDO::getId, id);
        return addressesMapper.selectById(id);
    }

    public List<AddressesDO> getByIds(List<Integer> ids) {
        LambdaQueryWrapper<AddressesDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AddressesDO::getId, ids);
        return addressesMapper.selectList(queryWrapper);
    }
}
