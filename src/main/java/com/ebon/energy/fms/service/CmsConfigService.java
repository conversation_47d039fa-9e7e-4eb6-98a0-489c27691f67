package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.CmsConfigDO;
import com.ebon.energy.fms.domain.entity.FmsUserDO;
import com.ebon.energy.fms.domain.po.CmsConfigCreatePO;
import com.ebon.energy.fms.domain.po.CmsConfigUpdatePO;
import com.ebon.energy.fms.domain.vo.CmsConfigVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.mapper.primary.CmsConfigMapper;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.ebon.energy.fms.util.StreamUtil.mapList;
import static com.ebon.energy.fms.util.StreamUtil.toMap;

@Slf4j
@Service
public class CmsConfigService {

    @Resource
    private CmsConfigMapper cmsConfigMapper;

    @Resource
    private UserService userService;

    public void create(CmsConfigCreatePO createPO) {
        Integer loginUserId = RequestUtil.getLoginUserId();

        CmsConfigDO cmsConfigDO = new CmsConfigDO();
        cmsConfigDO.setName(createPO.getName());
        cmsConfigDO.setCreatedBy(loginUserId);
        cmsConfigDO.setUpdatedBy(loginUserId);
        cmsConfigDO.setContent(createPO.getContent());
        cmsConfigDO.setStatus(createPO.getStatus() != null ? createPO.getStatus() : 0);
        cmsConfigMapper.insert(cmsConfigDO);
    }

    public void update(CmsConfigUpdatePO updatePO) {
        CmsConfigDO dbCmsDO = cmsConfigMapper.selectById(updatePO.getId());
        if (dbCmsDO == null) {
            throw new BizException("crm config not exist");
        }

        CmsConfigDO cmsConfigDO = new CmsConfigDO();
        cmsConfigDO.setId(updatePO.getId());
        cmsConfigDO.setName(updatePO.getName());
        cmsConfigDO.setContent(updatePO.getContent());
        cmsConfigDO.setUpdatedBy(RequestUtil.getLoginUserId());
        if (updatePO.getStatus() != null) {
            cmsConfigDO.setStatus(updatePO.getStatus());
        }
        cmsConfigMapper.updateById(cmsConfigDO);
    }

    public PageResult<CmsConfigVO> getCmsPage(String name, int current, int pageSize) {
        Page<CmsConfigDO> page = new Page<>(current, pageSize);
        LambdaQueryWrapper<CmsConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(name != null, CmsConfigDO::getName, name);
        queryWrapper.orderByDesc(CmsConfigDO::getUpdatedAt);
        Page<CmsConfigDO> cmsPage = cmsConfigMapper.selectPage(page, queryWrapper);
        if (cmsPage.getTotal() == 0) {
            return PageResult.toResponse(Collections.emptyList(), 0L, current, pageSize);
        }

        List<Integer> ids = mapList(cmsPage.getRecords(), CmsConfigDO::getCreatedBy);
        Map<Integer, FmsUserDO> userMap = userService.getUserMap(ids);

        return PageResult.toResponse(mapList(cmsPage.getRecords(), e -> {
            FmsUserDO createUser = userMap.get(e.getCreatedBy());
            FmsUserDO updateUser = userMap.get(e.getUpdatedBy());

            CmsConfigVO cmsConfigVO = new CmsConfigVO();
            cmsConfigVO.setId(e.getId());
            cmsConfigVO.setName(e.getName());
            cmsConfigVO.setContent(e.getContent());
            cmsConfigVO.setStatus(e.getStatus());
            cmsConfigVO.setCreatedByName(createUser != null ? createUser.getName() : null);
            cmsConfigVO.setUpdatedByName(updateUser != null ? updateUser.getName() : null);
            cmsConfigVO.setCreateTime(String.valueOf(e.getCreatedAt().getTime()));
            cmsConfigVO.setUpdateTime(String.valueOf(e.getUpdatedAt().getTime()));
            return cmsConfigVO;
        }), cmsPage.getTotal(), current, pageSize);
    }
}
