package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.EmailSendStatusEnum;
import com.ebon.energy.fms.config.EmailConfig;
import com.ebon.energy.fms.domain.entity.EmailDeliveryRecordDO;
import com.ebon.energy.fms.domain.vo.MailMessage;
import com.ebon.energy.fms.mapper.primary.EmailDeliveryRecordMapper;
import com.google.common.base.Strings;
import jakarta.mail.*;
import jakarta.mail.internet.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@Service
@Slf4j
public class EmailService {

    @Resource
    private EmailConfig emailConfig;

    @Resource
    private EmailDeliveryRecordMapper emailDeliveryRecordMapper;

    public void sendEmail(MailMessage msg, String serialNumber) throws MessagingException {
        MailMessage.checkMailMessage(msg);
        // 创建邮件会话
        MimeMessage message = buildMessage(msg, Boolean.FALSE);
        // 发送邮件
        try {
            Transport.send(message);
            log.info("邮件已成功提交到SMTP服务器！");
            addEmailSendRecord(msg,"", EmailSendStatusEnum.SUCCESS.getValue(), serialNumber);
        } catch (AuthenticationFailedException e) {
            log.info("认证失败: 用户名或密码错误");
            addEmailSendRecord(msg,"认证失败: 用户名或密码错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (SendFailedException e) {
            log.info("收件人地址错误或被服务器拒绝: " + e.getInvalidAddresses());
            addEmailSendRecord(msg,"收件人地址错误或被服务器拒绝", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (MessagingException e) {
            log.info("通信错误: " + e.getMessage());
            addEmailSendRecord(msg,"通信错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (Exception e) {
            log.info("未知错误: " + e.getMessage());
            addEmailSendRecord(msg,e.getMessage(), EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        }
    }

    public void sendAlertEmail(MailMessage msg, String serialNumber) throws MessagingException {
        MailMessage.checkMailMessage(msg);
        // 创建邮件会话
        MimeMessage message = buildMessage(msg, Boolean.TRUE);
        // 发送Alert邮件
        try {
            Transport.send(message);
            log.info("邮件已成功提交到SMTP服务器！");
            addEmailSendRecord(msg,"", EmailSendStatusEnum.SUCCESS.getValue(), serialNumber);
        } catch (AuthenticationFailedException e) {
            log.info("认证失败: 用户名或密码错误");
            addEmailSendRecord(msg,"认证失败: 用户名或密码错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (SendFailedException e) {
            log.info("收件人地址错误或被服务器拒绝: {}" , e.getInvalidAddresses());
            addEmailSendRecord(msg,"收件人地址错误或被服务器拒绝", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (MessagingException e) {
            log.info("通信错误: " + e.getMessage());
            addEmailSendRecord(msg,"通信错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (Exception e) {
            log.info("未知错误: " + e.getMessage());
            addEmailSendRecord(msg,e.getMessage(), EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        }

    }

    public void sendEmailWithAttachment(MailMessage msg, byte[] attachmentBytes, String attachmentName, String serialNumber)
            throws MessagingException {
        MailMessage.checkMailMessage(msg);
        // 创建邮件会话
        Properties props = new Properties();
        props.put("mail.smtp.host",  emailConfig.getSmtpHost());
        props.put("mail.smtp.port",  emailConfig.getSmtpPort());
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.timeout", "60000");

        Session session = Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(
                         emailConfig.getNetworkCredentialUserID(),
                         emailConfig.getNetworkCredentialPwd()
                );
            }
        });

        // 创建多部分邮件
        MimeMessage message = new MimeMessage(session);

        message.setFrom(new InternetAddress(msg.getFrom()));
        // 设置收件人
        for (String to : msg.getTo()) {
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
        }
        // 设置抄送
        if(!CollectionUtils.isEmpty(msg.getCc())) {
            message.setRecipients(Message.RecipientType.CC, parseAddresses(msg.getCc()));
        }

        if(!CollectionUtils.isEmpty(msg.getBcc())) {
            message.setRecipients(Message.RecipientType.BCC, parseAddresses(msg.getBcc()));
        }

        message.setSubject(msg.getSubject(), "UTF-8");
        // 创建多部分内容
        Multipart multipart = new MimeMultipart();

        // 添加HTML正文
        MimeBodyPart textPart = new MimeBodyPart();
        textPart.setContent(msg.getContent(), "text/html; charset=utf-8");
        multipart.addBodyPart(textPart);

        // 添加附件
        MimeBodyPart attachmentPart = new MimeBodyPart();
        attachmentPart.setFileName(attachmentName);
        attachmentPart.setContent(attachmentBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        attachmentPart.setDisposition(MimeBodyPart.ATTACHMENT);
        multipart.addBodyPart(attachmentPart);
        // 设置邮件内容
        message.setContent(multipart);
        // 发送邮件
        try {
            Transport.send(message);
            log.info("邮件已成功提交到SMTP服务器！");
            addEmailSendRecord(msg,"", EmailSendStatusEnum.SUCCESS.getValue(), serialNumber);
        } catch (AuthenticationFailedException e) {
            log.info("认证失败: 用户名或密码错误");
            addEmailSendRecord(msg,"认证失败: 用户名或密码错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (SendFailedException e) {
            log.info("收件人地址错误或被服务器拒绝");
            addEmailSendRecord(msg,"收件人地址错误或被服务器拒绝", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (MessagingException e) {
            log.info("通信错误: " + e.getMessage());
            addEmailSendRecord(msg,"通信错误", EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        } catch (Exception e) {
            log.info("未知错误: " + e.getMessage());
            addEmailSendRecord(msg,e.getMessage(), EmailSendStatusEnum.FAIL.getValue(), serialNumber);
        }
    }

    private MimeMessage buildMessage(MailMessage msg, Boolean isAlert) throws MessagingException {
        MimeMessage message = new MimeMessage(getSession(isAlert));
        message.setFrom(new InternetAddress(msg.getFrom()));
        message.setRecipients(Message.RecipientType.TO, parseAddresses(msg.getTo()));

        if(!CollectionUtils.isEmpty(msg.getCc())) {
            message.setRecipients(Message.RecipientType.CC, parseAddresses(msg.getCc()));
        }

        if(!CollectionUtils.isEmpty(msg.getBcc())) {
            message.setRecipients(Message.RecipientType.BCC, parseAddresses(msg.getBcc()));
        }

        message.setSubject(msg.getSubject(), "UTF-8");
        message.setContent(msg.getContent(), "text/html; charset=utf-8");
        return message;
    }


    private Address[] parseAddresses(List<String> addresses) throws AddressException {
        return addresses.stream()
                .map(a -> {
                    try {
                        return new InternetAddress(a);
                    } catch (AddressException e) {
                        throw new RuntimeException("Invalid address: " + a, e);
                    }
                })
                .toArray(Address[]::new);
    }

    private Session getSession(Boolean isAlert) {
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host",  emailConfig.getSmtpHost());
        props.put("mail.smtp.port",  emailConfig.getSmtpPort());
        props.put("mail.smtp.timeout", "60000");
        if(isAlert) {
            return Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(
                             emailConfig.getAlertNetworkCredentialUserID(),
                             emailConfig.getAlertNetworkCredentialPwd()
                    );
                }
            });
        } else {
            return Session.getInstance(props, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(
                             emailConfig.getNetworkCredentialUserID(),
                             emailConfig.getNetworkCredentialPwd()
                    );
                }
            });
        }
    }

    public void addEmailSendRecord(MailMessage msg, String failReason, String status, String serialNumber) {
        EmailDeliveryRecordDO deliveryRecordDO = new EmailDeliveryRecordDO();
        deliveryRecordDO.setFromAddress(msg.getFrom());
        deliveryRecordDO.setContent(msg.getSubject().equalsIgnoreCase("Initial Password")
                || msg.getSubject().equalsIgnoreCase("Password Reset") ? "" : msg.getContent());
        deliveryRecordDO.setSubject(msg.getSubject());
        deliveryRecordDO.setToAddress(String.join(",", msg.getTo()));
        deliveryRecordDO.setStatus(status);
        deliveryRecordDO.setFailReason(failReason);
        deliveryRecordDO.setSerialNumber(Strings.nullToEmpty(serialNumber));

        emailDeliveryRecordMapper.insert(deliveryRecordDO);
    }
}
