package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ebon.energy.fms.domain.entity.ErrorMappingDO;
import com.ebon.energy.fms.domain.vo.ErrorMappingVO;
import com.ebon.energy.fms.mapper.third.ErrorMappingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Service
@Slf4j
public class ErrorService {

    @Resource
    private ErrorMappingMapper errorMappingMapper;

    public List<ErrorMappingVO> getErrorMappings() {
        List<ErrorMappingDO> list = errorMappingMapper.selectList(new QueryWrapper<>());
        return mapList(list, e -> {
            ErrorMappingVO errorMappingVO = new ErrorMappingVO();
            errorMappingVO.setErrorCode(e.getId());
            errorMappingVO.setErrorCodeParsed(e.getName());
            errorMappingVO.setDescription(e.getDescription());
            return errorMappingVO;
        });
    }
}
