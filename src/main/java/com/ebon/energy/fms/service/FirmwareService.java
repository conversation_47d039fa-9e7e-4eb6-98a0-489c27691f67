package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.ModelTypeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.FirmwareVersionsDO;
import com.ebon.energy.fms.domain.entity.InvertersDO;
import com.ebon.energy.fms.domain.po.FirmwareCreatePO;
import com.ebon.energy.fms.domain.po.FirmwareDeletePO;
import com.ebon.energy.fms.domain.po.FirmwareUpdatePO;
import com.ebon.energy.fms.domain.po.FirmwareUpgradePO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.mapper.second.FirmwareVersionsMapper;
import com.ebon.energy.fms.util.RedbackWebApiUtil;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Service
@Slf4j
public class FirmwareService {

    @Resource
    private FirmwareVersionsMapper firmwareVersionsMapper;

    @Resource
    private ModelVersionRelationService modelVersionRelationService;

    @Resource
    private DeviceService deviceService;
    
    @Resource
    private InverterService inverterService;

    @Resource
    private RedbackWebApiUtil redbackWebApiUtil;

    /**
     * 获取型号关联的版本
     * @param modelId
     * @return
     */
    public List<String> getFirmwareVersionByModel(Integer modelId) {
        List<String> strings = modelVersionRelationService.listVersionsByModelId(modelId, ModelTypeEnum.Firmware);
        return null;
    }

    /**
     * 获取全部版本
     * @return
     */
    public List<FirmwareVersionDetailVO> getAllFirmwareVersion() {
        List<FirmwareVersionsDO> vos = firmwareVersionsMapper.selectList(new QueryWrapper<>());
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        return vos.stream().map(v -> {
            FirmwareVersionDetailVO firmwareVersionVO = new FirmwareVersionDetailVO();
            BeanUtils.copyProperties(v, firmwareVersionVO);
            firmwareVersionVO.setVersion(v.getDspVersion() + v.getDspVersion() + v.getArmVersion());
            firmwareVersionVO.setIsOfficial(v.getIsOfficialVersion());
            firmwareVersionVO.setCreator(v.getCreatedBy());
            firmwareVersionVO.setLastModifiedUtc(v.getLastModifiedUtc() != null ? String.valueOf(v.getLastModifiedUtc().getTime()) : null);
            return firmwareVersionVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取全部版本(格式化后)
     * @return
     */
    public List<FirmwareVersionDetailVO> getFirmwareVersion() {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getIsOfficialVersion, Boolean.TRUE);
        List<FirmwareVersionsDO> vos = firmwareVersionsMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        return vos.stream().map(v -> {
            FirmwareVersionDetailVO firmwareVersionVO = new FirmwareVersionDetailVO();
            firmwareVersionVO.setId(v.getId());
            firmwareVersionVO.setVersion(v.getDspVersion() + v.getDspVersion() + v.getArmVersion());
            firmwareVersionVO.setIsOfficial(v.getIsOfficialVersion());
            firmwareVersionVO.setCreator(v.getCreatedBy());
            firmwareVersionVO.setLastModifiedUtc(v.getLastModifiedUtc() != null ? String.valueOf(v.getLastModifiedUtc().getTime()) : null);
            return firmwareVersionVO;
        }).collect(Collectors.toList());
    }


    /**
     * 获取版本详情
     * @return
     */
    public FirmwareVersionDetailVO getFirmwareVersionDetail(Integer id) {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getId, id);
        FirmwareVersionsDO vos = firmwareVersionsMapper.selectOne(wrapper);
        FirmwareVersionDetailVO vo = new FirmwareVersionDetailVO();
        if (Objects.isNull(vos)) {
            return vo;
        }

        BeanUtils.copyProperties(vos, vo);
        vo.setCreator(vos.getCreatedBy());
        vo.setLastModifiedUtc(String.valueOf(vos.getLastModifiedUtc().getTime()));
        vo.setIsOfficial(vos.getIsOfficialVersion());
        return vo;
    }

    public PageResult<String> getSnPageByVersion(ModelTypeEnum type, String version, int current, int pageSize) {
        FirmwareVersionsDO firmwareVersion = firmwareVersionsMapper.selectByVersion(version);
        Page<InvertersDO> inverterPage = inverterService.getPageByVersion(type, firmwareVersion.getId().toString(), current, pageSize);
        return PageResult.toResponse(mapList(inverterPage.getRecords(), InvertersDO::getSerialNumber), inverterPage.getTotal(), current, pageSize);
    }

    /**
     * 更新固件
     * @param po
     * @return
     */
    public List<FirmwareUpdateErrorVO> upgrade(FirmwareUpgradePO po) {
        String email = RequestUtil.getLoginUserEmail();
        List<FirmwareUpdateErrorVO> errors = new ArrayList<>();

        for (int i = 0; i < po.getSerialNumbers().size(); i++) {
            String serialNumber = po.getSerialNumbers().get(i);

            try {
                DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Ross.name());
                if (Objects.isNull(device)) {
                    throw new BizException("404", "Device not found");
                }

                redbackWebApiUtil.updateDeviceTwinAsync(serialNumber, ApplicationName.Ross.name(), po.getFullTwinJson(), email);
            } catch (Exception e) {
                FirmwareUpdateErrorVO vo =  FirmwareUpdateErrorVO.builder()
                        .serialNumber(serialNumber)
                        .error(e.getMessage())
                        .build();
                errors.add(vo);
            }
        }
        return errors;
    }

    public void create(FirmwareCreatePO po) {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getArmVersion, po.getArmVersion())
                .eq(FirmwareVersionsDO::getDspVersion, po.getDspVersion());
        boolean exists = firmwareVersionsMapper.exists(wrapper);
        if (exists) {
            return;
        }
        FirmwareVersionsDO versionsDO = new FirmwareVersionsDO();
        String email = RequestUtil.getLoginUserEmail();
        versionsDO.setCreatedBy(email);
        versionsDO.setArmVersion(po.getArmVersion());
        versionsDO.setDspVersion(po.getDspVersion());
        versionsDO.setIsOfficialVersion(po.getIsOfficialVersion());
        versionsDO.setLastModifiedUtc(new Date());
        firmwareVersionsMapper.insert(versionsDO);
    }

    public void update(FirmwareUpdatePO po) {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getId, po.getId());
        boolean exists = firmwareVersionsMapper.exists(wrapper);

        if (!exists) {
            throw new BizException("record not exist");
        }

        FirmwareVersionsDO versionsDO = new FirmwareVersionsDO();
        versionsDO.setArmVersion(po.getArmVersion());
        versionsDO.setDspVersion(po.getDspVersion());
        versionsDO.setIsOfficialVersion(po.getIsOfficialVersion());
        versionsDO.setLastModifiedUtc(new Date());
        firmwareVersionsMapper.update(versionsDO, wrapper);
    }

    public void delete(FirmwareDeletePO po) {
        LambdaQueryWrapper<FirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FirmwareVersionsDO::getId, po.getId());
        boolean exists = firmwareVersionsMapper.exists(wrapper);

        if (!exists) {
            return;
        }

        firmwareVersionsMapper.delete(wrapper);
    }

    ///  function createUUID() {
    ///     return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    ///         var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    ///         return v.toString(16);
    ///     });
    /// }
}
