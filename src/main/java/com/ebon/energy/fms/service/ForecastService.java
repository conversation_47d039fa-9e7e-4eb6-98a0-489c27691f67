package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.domain.entity.ProductDailyForecastDO;
import com.ebon.energy.fms.domain.vo.DayForecastVO;
import com.ebon.energy.fms.mapper.primary.ProductDailyForecastMapper;
import com.ebon.energy.fms.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Service
@Slf4j
public class ForecastService {

    @Resource
    private ProductDailyForecastMapper productDailyForecastMapper;

    @Value("${forecast.url}")
    private String forecastUrl;

    public List<String> getInverterSns() {
        String res = HttpClientUtil.get(forecastUrl + "/api/generation/list-sns");
        return JSONObject.parseArray(res, String.class);
    }

    public List<String> getFeaturedSns() {
        String res = HttpClientUtil.get(forecastUrl + "/api/generation/featured-sns");
        return JSONObject.parseArray(res, String.class);
    }

    public Map<String, BigDecimal> getDailyGeneration(String sn) {
        String res = HttpClientUtil.get(forecastUrl + "/api/generation/predict?sn=" + sn);
        return JSONObject.parseObject(res, HashMap.class);
    }

    public void saveBatch(List<ProductDailyForecastDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        productDailyForecastMapper.saveBatch(list);
    }

    public List<DayForecastVO> getDayForecastList(String sn, String startTime, String endTime) {
        List<ProductDailyForecastDO> forecastList = productDailyForecastMapper.selectInRange(sn, startTime, endTime);
        List<DayForecastVO> res = mapList(forecastList, e -> {
            DayForecastVO forecastVO = new DayForecastVO(e.getDate(), "", null, e.getGeneration());
            return forecastVO;
        });
        return res;
    }
}
