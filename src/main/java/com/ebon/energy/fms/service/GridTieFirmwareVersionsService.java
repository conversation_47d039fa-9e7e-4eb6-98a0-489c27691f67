package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.GridTieFirmwareVersionsDO;
import com.ebon.energy.fms.domain.po.FirmwareDeletePO;
import com.ebon.energy.fms.domain.po.GridTieFirmwareVersionsCreatePO;
import com.ebon.energy.fms.domain.po.GridTieFirmwareVersionsEditPO;
import com.ebon.energy.fms.domain.po.GridTieFirmwareVersionsUpdatePO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.domain.vo.GridTieFirmwareVersionsUpdateErrorVO;
import com.ebon.energy.fms.domain.vo.GridTieFirmwareVersionsVO;
import com.ebon.energy.fms.mapper.second.GridTieFirmwareVersionsMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.util.RedbackWebApiUtil;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.tuya.TuyaApiUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GridTieFirmwareVersionsService {

    @Resource
    private GridTieFirmwareVersionsMapper gridTieFirmwareVersionsMapper;

    @Resource
    private DeviceService deviceService;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private TuyaApiUtil tuyaApiUtil;

    @Resource
    private RedbackWebApiUtil redbackWebApiUtil;

    @Value("${EMSStorageContainerURL}")
    @Getter
    private String storageContainerURL;

    public List<GridTieFirmwareVersionsVO> getList(Boolean isOfficialVersion) {
        LambdaQueryWrapper<GridTieFirmwareVersionsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(isOfficialVersion != null, GridTieFirmwareVersionsDO::getIsOfficialVersion, isOfficialVersion);
        List<GridTieFirmwareVersionsDO> list = gridTieFirmwareVersionsMapper.selectList(queryWrapper);

        List<GridTieFirmwareVersionsVO> result = list.stream().map(e -> {
            GridTieFirmwareVersionsVO vo = new GridTieFirmwareVersionsVO();
            BeanUtils.copyProperties(e, vo);
            vo.setId(String.valueOf(e.getId()));
            vo.setLastModifiedUtc(e.getLastModifiedUtc() != null ? String.valueOf(e.getLastModifiedUtc().getTime()) : null);
            return vo;
        }).collect(Collectors.toList());

        return result;
    }

    public GridTieFirmwareVersionsVO getVersionDetail(String id) {
        LambdaQueryWrapper<GridTieFirmwareVersionsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GridTieFirmwareVersionsDO::getId, id);
        GridTieFirmwareVersionsDO detail = gridTieFirmwareVersionsMapper.selectOne(queryWrapper);
        if (Objects.isNull(detail)) {
            throw new BizException("404", "Version not found");
        }
        GridTieFirmwareVersionsVO vo = new GridTieFirmwareVersionsVO();
        BeanUtils.copyProperties(detail, vo);
        vo.setId(String.valueOf(detail.getId()));
        return vo;
    }

    public List<GridTieFirmwareVersionsUpdateErrorVO> update(GridTieFirmwareVersionsUpdatePO po) throws Exception {
        String userId = RequestUtil.getLoginUserEmail();

        List<GridTieFirmwareVersionsUpdateErrorVO> errors = new ArrayList<>();
        for (int i = 0; i < po.getSerialNumbers().size(); i++) {
            String serialNumber = po.getSerialNumbers().get(i);
            try {
                DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Ross.name());
                if (Objects.isNull(device)) {
                    throw new BizException("404", "Device not found");
                }

                if (CloudPlatformName.Tuya.name().equalsIgnoreCase(device.getCloudPlatformName())) {
                    tuyaApiUtil.updateTuyaDeviceShadowDesired(device.getDeviceId(), po.getFullTwinJson());
                } else {
                    redbackWebApiUtil.updateDeviceTwinAsync(serialNumber, ApplicationName.Ross.name(), po.getFullTwinJson(), userId);
                }
            } catch (Exception e) {
                GridTieFirmwareVersionsUpdateErrorVO vo =  GridTieFirmwareVersionsUpdateErrorVO.builder()
                        .serialNumber(serialNumber)
                        .error(e.getMessage())
                        .build();
                errors.add(vo);
            }
        }

        return errors;
    }

    public void edit(GridTieFirmwareVersionsEditPO po) {
        LambdaQueryWrapper<GridTieFirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GridTieFirmwareVersionsDO::getId, po.getId());
        boolean exists = gridTieFirmwareVersionsMapper.exists(wrapper);

        if (!exists) {
            throw new BizException("record not exist");
        }

        GridTieFirmwareVersionsDO versionsDO = new GridTieFirmwareVersionsDO();
        versionsDO.setEmsFirmwareVersion(po.getEmsFirmwareVersion());
        versionsDO.setBinFileName(po.getBinFileName());
        versionsDO.setIsOfficialVersion(po.getIsOfficialVersion());
        versionsDO.setLastModifiedUtc(new Timestamp(new Date().getTime()));
        gridTieFirmwareVersionsMapper.update(versionsDO, wrapper);
    }

    public void create(GridTieFirmwareVersionsCreatePO po) {
        LambdaQueryWrapper<GridTieFirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GridTieFirmwareVersionsDO::getEmsFirmwareVersion, po.getEmsFirmwareVersion())
                .eq(GridTieFirmwareVersionsDO::getBinFileName, po.getBinFileName());
        boolean exists = gridTieFirmwareVersionsMapper.exists(wrapper);
        if (exists) {
            return;
        }
        GridTieFirmwareVersionsDO versionsDO = new GridTieFirmwareVersionsDO();
        String email = RequestUtil.getLoginUserEmail();
        versionsDO.setCreatedBy(email);
        versionsDO.setEmsFirmwareVersion(po.getEmsFirmwareVersion());
        versionsDO.setBinFileName(po.getBinFileName());
        versionsDO.setIsOfficialVersion(po.getIsOfficialVersion());
        versionsDO.setLastModifiedUtc(new Timestamp(new Date().getTime()));
        gridTieFirmwareVersionsMapper.insert(versionsDO);
    }

    public void delete(FirmwareDeletePO po) {
        LambdaQueryWrapper<GridTieFirmwareVersionsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GridTieFirmwareVersionsDO::getId, po.getId());
        boolean exists = gridTieFirmwareVersionsMapper.exists(wrapper);

        if (!exists) {
            return;
        }

        gridTieFirmwareVersionsMapper.delete(wrapper);
    }
}
