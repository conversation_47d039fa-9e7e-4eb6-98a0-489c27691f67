package com.ebon.energy.fms.service;

import com.ebon.energy.fms.domain.vo.InstallerCompanyVO;
import com.ebon.energy.fms.mapper.third.RedbackUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class InstallerService {

    @Resource
    private RedbackUserMapper redbackUserMapper;

    public List<InstallerCompanyVO> getInstallerCompanys() {
        return redbackUserMapper.selectInstallerCompanys();
    }
}
