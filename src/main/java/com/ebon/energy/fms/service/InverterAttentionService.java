package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.domain.entity.InverterAttentionDO;
import com.ebon.energy.fms.mapper.primary.InverterAttentionMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class InverterAttentionService {

    @Resource
    private InverterAttentionMapper inverterAttentionMapper;

    public InverterAttentionDO getBySn(String sn) {
        return inverterAttentionMapper.selectById(sn);
    }

    public List<InverterAttentionDO> getByIds(List<String> ids) {
        List<InverterAttentionDO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            return list;
        }

        List<List<String>> batches = Lists.partition(ids, 2000);
        for (List<String> batch : batches) {
            LambdaQueryWrapper<InverterAttentionDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(InverterAttentionDO::getRedbackProductSn, batch);
            list.addAll(inverterAttentionMapper.selectList(queryWrapper));
        }

        return list;
    }

    public void save(InverterAttentionDO attentionDO) {
        inverterAttentionMapper.insert(attentionDO);
    }

    public void update(InverterAttentionDO attentionDO) {
        inverterAttentionMapper.updateById(attentionDO);
    }

    public void updateErrorProcessed(String sn) {
        InverterAttentionDO attentionDO = new InverterAttentionDO();
        attentionDO.setRedbackProductSn(sn);
        attentionDO.setStatus(1);
        inverterAttentionMapper.updateById(attentionDO);
    }
}
