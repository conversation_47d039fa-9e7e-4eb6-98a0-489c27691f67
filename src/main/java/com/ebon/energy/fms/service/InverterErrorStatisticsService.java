package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsDO;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsDetailDO;
import com.ebon.energy.fms.domain.entity.InverterErrorStatisticsExtDO;
import com.ebon.energy.fms.domain.po.ErrorStatisticsQueryPO;
import com.ebon.energy.fms.domain.vo.ErrorOccurDateVO;
import com.ebon.energy.fms.domain.vo.InverterErrorStatisticsVO;
import com.ebon.energy.fms.domain.vo.PageResult;
import com.ebon.energy.fms.mapper.primary.InverterErrorStatisticsDetailMapper;
import com.ebon.energy.fms.mapper.primary.InverterErrorStatisticsMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.*;

@Slf4j
@Service
public class InverterErrorStatisticsService {

    @Resource
    private InverterErrorStatisticsMapper inverterErrorStatisticsMapper;
    
    @Resource
    private InverterErrorStatisticsDetailMapper inverterErrorStatisticsDetailMapper;

    public PageResult<InverterErrorStatisticsVO> getErrorStatisticsPage(ErrorStatisticsQueryPO queryPO) {
        Page<InverterErrorStatisticsDO> page = new Page<>(queryPO.getCurrent(), queryPO.getPageSize());
        LambdaQueryWrapper<InverterErrorStatisticsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(queryPO.getRedbackProductSn() != null, InverterErrorStatisticsDO::getRedbackProductSn, queryPO.getRedbackProductSn());
        queryWrapper.like(queryPO.getErrorCode() != null, InverterErrorStatisticsDO::getErrorCode, queryPO.getErrorCode());

        if (StringUtils.isNotBlank(queryPO.getStartDate())) {
            queryWrapper.ge(InverterErrorStatisticsDO::getDate, queryPO.getStartDate());
        }

        if (StringUtils.isNotBlank(queryPO.getEndDate())) {
            queryWrapper.le(InverterErrorStatisticsDO::getDate, queryPO.getEndDate());
        }

        queryWrapper.orderByDesc(InverterErrorStatisticsDO::getDate).orderByDesc(InverterErrorStatisticsDO::getCount)
                .orderByDesc(InverterErrorStatisticsDO::getUpdatedAt);
        Page<InverterErrorStatisticsDO> statPage = inverterErrorStatisticsMapper.selectPage(page, queryWrapper);
        if (statPage.getTotal() == 0) {
            return PageResult.toResponse(Collections.emptyList(), 0L, queryPO.getCurrent(), queryPO.getPageSize());
        }

        List<Integer> ids = mapList(statPage.getRecords(), InverterErrorStatisticsDO::getId);
        List<InverterErrorStatisticsDetailDO> allDetails = inverterErrorStatisticsDetailMapper.selectByStatisticsIds(ids);
        Map<Integer, List<InverterErrorStatisticsDetailDO>> detailGroup = groupBy(allDetails, InverterErrorStatisticsDetailDO::getStatisticsId);

        return PageResult.toResponse(mapList(statPage.getRecords(), e -> {
            InverterErrorStatisticsVO statisticsVO = new InverterErrorStatisticsVO();
            BeanUtils.copyProperties(e, statisticsVO);

            List<InverterErrorStatisticsDetailDO> details = detailGroup.get(e.getId());
            statisticsVO.setOccurDates(mapList(details, d -> {
                ErrorOccurDateVO dateVO = new ErrorOccurDateVO();
                dateVO.setFirstTime(d.getFirstTime());
                dateVO.setLastTime(d.getLastTime());
                return dateVO;
            }));
            return statisticsVO;
        }), statPage.getTotal(), queryPO.getCurrent(), queryPO.getPageSize());
    }

    public List<InverterErrorStatisticsExtDO> getByDateBatch(String date, List<String> serialNumbers) {
        List<InverterErrorStatisticsExtDO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(serialNumbers)) {
            return list;
        }

        List<List<String>> batches = Lists.partition(serialNumbers, 2000);
        for (List<String> batch : batches) {
            list.addAll(inverterErrorStatisticsMapper.selectByDate(date, batch));
        }
        
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<InverterErrorStatisticsExtDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<List<InverterErrorStatisticsExtDO>> batches = Lists.partition(list, 400);
        List<InverterErrorStatisticsDetailDO> details = new ArrayList<>();
        for (List<InverterErrorStatisticsExtDO> batch : batches) {
            List<InverterErrorStatisticsDO> dos = mapList(batch, e -> {
                InverterErrorStatisticsDO statisticsDO = new InverterErrorStatisticsDO();
                BeanUtils.copyProperties(e, statisticsDO);
                return statisticsDO;
            });

            List<InverterErrorStatisticsDO> newList = inverterErrorStatisticsMapper.insertBatchWithOutput(dos);
            Map<String, InverterErrorStatisticsDO> newMap = toMap(newList, e -> e.getRedbackProductSn() + "_" + e.getDate() + "_" + e.getErrorCode());

            for (InverterErrorStatisticsExtDO extDO : batch) {
                InverterErrorStatisticsDO statisticsDO = newMap.get(extDO.getRedbackProductSn() + "_" + extDO.getDate() + "_" + extDO.getErrorCode());
                if (statisticsDO != null) {
                    extDO.setId(statisticsDO.getId());
                } else {
                    continue;
                }

                for (InverterErrorStatisticsDetailDO detail : extDO.getDetails()) {
                    detail.setStatisticsId(extDO.getId());
                }
                details.addAll(extDO.getDetails());
            }
        }

        insertDetailBatch(details);
    }

    public void insertDetailBatch(List<InverterErrorStatisticsDetailDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<List<InverterErrorStatisticsDetailDO>> batches = Lists.partition(list, 600);
        for (List<InverterErrorStatisticsDetailDO> batch : batches) {
            inverterErrorStatisticsDetailMapper.insertBatch(batch);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatchWithDetail(List<InverterErrorStatisticsExtDO> list, List<InverterErrorStatisticsDetailDO> details) {
        updateBatch(list);
        insertDetailBatch(details);
    }

    public void updateBatch(List<InverterErrorStatisticsExtDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<List<InverterErrorStatisticsExtDO>> batches = Lists.partition(list, 400);
        for (List<InverterErrorStatisticsExtDO> batch : batches) {
            inverterErrorStatisticsMapper.updateBatch(batch);
        }
    }

    public void updateDetailBatch(List<InverterErrorStatisticsDetailDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<List<InverterErrorStatisticsDetailDO>> batches = Lists.partition(list, 1000);
        for (List<InverterErrorStatisticsDetailDO> batch : batches) {
            inverterErrorStatisticsDetailMapper.updateBatch(batch);
        }
    }
}
