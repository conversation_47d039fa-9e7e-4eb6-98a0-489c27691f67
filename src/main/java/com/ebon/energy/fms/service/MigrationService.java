package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.AddressesDO;
import com.ebon.energy.fms.domain.vo.DeviceVO;
import com.ebon.energy.fms.domain.vo.RossVersionsUpdateErrorVO;
import com.ebon.energy.fms.mapper.third.AddressesMapper;
import com.ebon.energy.fms.mapper.third.DeviceMapper;
import com.ebon.energy.fms.util.RedbackWebApiUtil;
import com.ebon.energy.fms.util.azure.IoTHubUtility;
import com.ebon.energy.fms.util.tuya.TuyaApiUtil;
import com.microsoft.azure.sdk.iot.service.twin.Twin;
import com.microsoft.azure.sdk.iot.service.twin.TwinCollection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MigrationService {
    
    @Resource
    private DeviceService deviceService;

    @Resource
    private TuyaApiUtil tuyaApiUtil;

    @Resource
    private IoTHubUtility ioTHubUtility;

    public String get(String serialNumber, String applicationName) {
        Twin twin = ioTHubUtility.getDeviceTwin(serialNumber, applicationName);
        return JSON.toJSONString(twin.getDesiredProperties());
    }

    public Map<String, Object> getBatch(List<String> serialNumbers, String applicationName) {
        if (CollectionUtils.isEmpty(serialNumbers)) {
            throw new BizException("no serialNumbers");
        }

        Map<String, Object> res = new HashMap<>();

        List<String> errors = new ArrayList<>();
        for (int i = 0; i < serialNumbers.size(); i++) {
            String serialNumber = serialNumbers.get(i);

            try {
                Twin twin = ioTHubUtility.getDeviceTwin(serialNumber, applicationName);
                TwinCollection properties = twin.getDesiredProperties();
                String jsonString = JSON.toJSONString(twin.getDesiredProperties());
                JSONObject parse = JSON.parseObject(jsonString);
                JSONObject windows = parse.getJSONObject("windows");
                String rossVersion = windows.getJSONObject("apps").getJSONObject("RedbackTechnologies_ROSS_3ym0hckg60t6j").getString("version");
                String wdVersion = windows.getJSONObject("apps").getJSONObject("RedbackTechnologies_Watchdog_00xfq0dct0ttr").getString("version");

                String reported = JSON.toJSONString(twin.getReportedProperties());
                JSONObject wdparse = JSON.parseObject(reported);
                JSONObject wdWindows = wdparse.getJSONObject("windows");
                JSONObject wdrossVersion = wdWindows.getJSONObject("apps").getJSONObject("RedbackTechnologies_ROSS_3ym0hckg60t6j");
                JSONObject wdwdVersion = wdWindows.getJSONObject("apps").getJSONObject("RedbackTechnologies_Watchdog_00xfq0dct0ttr");

                String connectionState = twin.getConnectionState();

                Map<String, Object> inner = new HashMap<>();
                inner.put("desired", "rossVersion:" + rossVersion + "." + "wdVersion:" + wdVersion);
                inner.put("reported-ross", wdrossVersion);
                inner.put("reported-watchdog", wdwdVersion);
                inner.put("connectionState", connectionState);

                res.put(serialNumber, inner);
            } catch (Exception e) {
                log.error("serialNumber error: {}", e);
            }
        }

        return res;
    }

    public void migration(String serialNumber, String applicationName, String settings) throws Exception {
        DeviceVO device = deviceService.getLastDevice(serialNumber, applicationName);

        if (ApplicationName.Ross.name().equalsIgnoreCase(applicationName)) {
            tuyaApiUtil.updateTuyaDeviceShadowDesired(device.getEdgeId(), settings);
        } else {
            tuyaApiUtil.updateTuyaDeviceShadowDesired(device.getDeviceId().replaceAll("-", ""), settings);
        }
    }

    public List<RossVersionsUpdateErrorVO> migrationBatch(List<String> serialNumbers, String applicationName) throws Exception {
        if (CollectionUtils.isEmpty(serialNumbers)) {
            throw new BizException("no serialNumbers");
        }

        // 创建线程池，限制最大线程数避免资源耗尽
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(serialNumbers.size(), 200));

        // 存储异步任务结果
        List<CompletableFuture<RossVersionsUpdateErrorVO>> futures = new ArrayList<>();
        List<RossVersionsUpdateErrorVO> errors = Collections.synchronizedList(new ArrayList<>());

        for (String serialNumber : serialNumbers) {

            CompletableFuture<RossVersionsUpdateErrorVO> future = CompletableFuture.supplyAsync(() -> {
                try {
                    String string = get(serialNumber, applicationName);
                    migration(serialNumber, applicationName, string);
                    return null; // 成功返回null
                } catch (Exception e) {
                    return RossVersionsUpdateErrorVO.builder()
                            .serialNumber(serialNumber)
                            .error(e.getMessage())
                            .build();
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 过滤出非null的错误结果
        for (CompletableFuture<RossVersionsUpdateErrorVO> future : futures) {
            RossVersionsUpdateErrorVO error = future.join();
            if (error != null) {
                errors.add(error);
            }
        }

        executor.shutdown();
        return errors;
    }
}
