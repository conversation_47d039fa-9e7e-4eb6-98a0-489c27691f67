package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.InverterInfoVO;
import com.ebon.energy.fms.domain.vo.ProductVO;
import com.ebon.energy.fms.domain.vo.telemetry.InverterStatus;
import com.ebon.energy.fms.domain.vo.telemetry.OuijaBoard;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.ProductDailyCachesMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.util.SafeAccess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.utils.ProductUtilities.isOnline;
import static com.ebon.energy.fms.util.SafeAccess.getOrElse;

@Slf4j
@Service
public class ProductService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductDailyCachesMapper productDailyCachesMapper;

    public ProductVO getProduct(String sn) {
        LambdaQueryWrapper<RedbackProductsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RedbackProductsDO::getRedbackProductSn, sn);
        RedbackProductsDO productsDO = productMapper.selectOne(queryWrapper);
        if (productsDO == null) {
            return null;
        }

        ProductVO productVO = new ProductVO();
        BeanUtils.copyProperties(productsDO, productVO);
        return productVO;
    }

    public List<RedbackProductsDO> getProducts(List<String> ids) {
        LambdaQueryWrapper<RedbackProductsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RedbackProductsDO::getRedbackProductSn, ids);
        return productMapper.selectList(queryWrapper);
    }

    public ModelInfoDO getModelInfo(String sn) {
        return productMapper.selectModelInfo(sn);
    }

    public InverterInfoVO getWithInstallation(String sn) {
        ProductWithInstallationDO productDO = productMapper.selectWithInstallation(sn);
        if (productDO == null) {
            throw new BizException("The serial number doesn't exist.");
        }

        InverterInfoVO inverterInfoVO = new InverterInfoVO();
        BeanUtils.copyProperties(productDO, inverterInfoVO);

        if (StringUtils.isNotBlank(productDO.getLatestSystemStatus())) {
            SystemStatus systemStatus = JSONObject.parseObject(productDO.getLatestSystemStatus(), SystemStatus.class);
            inverterInfoVO.setSystemStatus(systemStatus);

            inverterInfoVO.setIsOnline(isOnline(systemStatus.getDate(), 600000L));
            inverterInfoVO.setIsHourlyOnline(isOnline(systemStatus.getDate(), 3600000L));
            inverterInfoVO.setIsDailyOnline(isOnline(systemStatus.getDate(), 86400000L));

            inverterInfoVO.setInverterMode(SafeAccess.getValue(systemStatus, SystemStatus::getInverter, e -> e.getInverterMode() != null ? e.getInverterMode().getDisplayName() : null));
            inverterInfoVO.setFirmwareVersion(SafeAccess.getValue(systemStatus, SystemStatus::getInverter, InverterStatus::getFirmwareVersion));

            if (StringUtils.isBlank(inverterInfoVO.getRossVersion())) {
                String rossVersion = SafeAccess.getValue(systemStatus, SystemStatus::getOuijaBoard, OuijaBoard::getSoftwareVersion);
                inverterInfoVO.setRossVersion(rossVersion);
            }
        } else {
            inverterInfoVO.setIsOnline(false);
            inverterInfoVO.setIsHourlyOnline(false);
            inverterInfoVO.setIsDailyOnline(false);
        }

        return inverterInfoVO;
    }

    public ProductWithOwnerDO getProductWithOwner(String sn) {
        return productMapper.selectWithOwner(sn);
    }

    public List<DailyTotalVO> getNDayTotals(String serialNumber, LocalDate todayInSiteLocalTime, int days) {
        if (StringUtils.isBlank(serialNumber)) {
            throw new BizException("serialNumber cannot be null or empty");
        }

        // 计算日期范围
        LocalDate nDaysAgo = todayInSiteLocalTime.minusDays(days + 1);
        LocalDate startOfMonth = LocalDate.of(nDaysAgo.getYear(), nDaysAgo.getMonth(), 1);
        LocalDate start = startOfMonth.isBefore(nDaysAgo) ? startOfMonth : nDaysAgo;
        LocalDateTime endOfDay = LocalDateTime.of(
                todayInSiteLocalTime,
                LocalTime.of(23, 59, 59, 0)// 23:59:59
        );

        Timestamp startLocalDay = new Timestamp(start.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli());
        Timestamp endOfTodayLocal = new Timestamp(endOfDay.toInstant(ZoneOffset.UTC).toEpochMilli());

        List<ProductDailyHistoryDO> list = productDailyCachesMapper.selectEnergyDailyHistory(serialNumber, startLocalDay, endOfTodayLocal);
        return list.stream()
                .limit(days).map(e -> {
                    DailyTotalVO totalVO = new DailyTotalVO();
                    BeanUtils.copyProperties(e, totalVO);
                    return totalVO;
                }).collect(Collectors.toList());
    }
}
