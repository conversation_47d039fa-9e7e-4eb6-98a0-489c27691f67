package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.RedbackJobs;
import com.ebon.energy.fms.domain.vo.charts.RedbackJobHistory;
import com.ebon.energy.fms.repository.IRedbackJobHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Redback作业历史服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedbackJobHistoryService {

    private final IRedbackJobHistoryRepository redbackJobHistoryRepository;

    /**
     * 根据作业获取最新的作业历史记录
     *
     * @param job 作业枚举
     * @return 作业历史记录
     */
    public RedbackJobHistory getLatestJobHistory(RedbackJobs job) {
        log.debug("获取作业历史记录，作业名称: {}", job);
        return redbackJobHistoryRepository.getLatestJobHistoryAsync(job);
    }

    /**
     * 获取存储缩减作业列表
     *
     * @return 存储缩减作业历史记录列表
     */
    public List<RedbackJobHistory> getStorageReductionJobs() {
        log.debug("获取存储缩减作业列表");
        return redbackJobHistoryRepository.getStorageReductionJobsAsync();
    }

    /**
     * 检查指定作业是否在存储缩减作业列表中
     *
     * @param job 作业枚举
     * @return 如果在列表中返回true，否则返回false
     */
    public boolean isStorageReductionJob(RedbackJobs job) {
        List<RedbackJobHistory> storageReductionJobs = getStorageReductionJobs();
        return storageReductionJobs.stream()
                .anyMatch(jobHistory -> job.toString().equals(jobHistory.getJobName()));
    }
}
