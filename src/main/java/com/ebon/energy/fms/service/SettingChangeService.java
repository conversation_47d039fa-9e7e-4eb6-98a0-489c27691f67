package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SettingsReaderProvider;
import com.ebon.energy.fms.domain.vo.SettingChangeRequest;
import com.ebon.energy.fms.domain.vo.SiteSettingsChangeRequestVO;
import com.ebon.energy.fms.domain.vo.setting.LogicalSettingsChangeRequestDto;
import com.ebon.energy.fms.domain.vo.setting.SiteSettingsChangeRequestDto;
import com.ebon.energy.fms.domain.vo.setting.provider.SettingsBuilderProvider;
import com.ebon.energy.fms.repository.impl.SettingsServiceImpl;
import com.ebon.energy.fms.util.JsonUtils;
import com.ebon.energy.fms.util.RequestUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SettingChangeService {

    private final SettingsServiceImpl settingsService;
    private final ObjectMapper objectMapper;


    public void apply(String serialNumber, String changeRequestId, List<LogicalSettingsChangeRequestDto> changeRequest, boolean updateDeviceSettings) {
        var settingsDtoAsync = settingsService.getSettingsDtoAsync(serialNumber);
        var reader = SettingsReaderProvider.get(settingsDtoAsync);
        var builder = SettingsBuilderProvider.get(settingsDtoAsync);

        List<SettingChangeRequest> settingChangeRequests;

        try {
            settingChangeRequests = changeRequest
                    .stream()
                    .map(x -> new SettingChangeRequest(x.getTargetSetting(), getTargetValueObject(x.getTargetSetting(), x.getTargetValue())))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            try {
                String body = JsonUtils.toJson(changeRequest);
                log.error("Failed extracting change requests for {}. Ex: {}. Body: '{}'",
                        serialNumber, e.getMessage(), body);
            } catch (Exception e2) {
                log.error("Failed logging change requests for {}. Ex: '{}'",
                        serialNumber, e2.getMessage());
            }
            throw e;
        }

        for (var settingChange : settingChangeRequests) {

            if (settingChange.getTargetSetting() == UniversalSettingId.SITE_COORDINATOR) {
                if (settingChange.getRequest() instanceof SiteSettingsChangeRequestVO) {
                    SiteSettingsChangeRequestVO request = (SiteSettingsChangeRequestVO) settingChange.getRequest();
                    builder.addSiteCoordinator(request);
                } else {
                    throw new BizException("Unsupported setting type: " + settingChange.getRequest().getClass().getName());
                }
            } else {

                // TODO: 实现具体的设置应用逻辑
                throw new UnsupportedOperationException("Setting application logic not implemented yet");

            }

        }
        var patch = builder.toPatchString();

        settingsService.updateDesiredDeviceSettingsFromJsonPatch(RequestUtil.getPortolUserId(), serialNumber, patch.getJson(), patch.getIntent(), true);
    }

    /**
     * 根据设置类型和目标值获取目标值对象
     *
     * @param targetSetting 目标设置类型
     * @param targetValue   目标值字符串
     * @return 解析后的目标值对象
     */
    private Object getTargetValueObject(UniversalSettingId targetSetting, String targetValue) {
        if (targetValue == null) {
            return null;
        }

        try {
            switch (targetSetting) {
                case SITE_COORDINATOR:
                    return objectMapper.readValue(targetValue, SiteSettingsChangeRequestVO.class);


                default:
                    throw new BizException("Unsupported setting type: " + targetSetting);
            }
        } catch (Exception e) {
            log.error("Failed to parse target value for setting {}: {}", targetSetting, e.getMessage());
            throw new BizException("Failed to parse target value for setting: " + targetSetting);
        }
    }


}
