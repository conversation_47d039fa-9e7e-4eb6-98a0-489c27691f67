package com.ebon.energy.fms.service;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.EnergyBalancer;
import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.site.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.site.AllAboutSiteOverview;
import com.ebon.energy.fms.domain.vo.site.SiteHistoryVO;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import com.ebon.energy.fms.mapper.third.MetricsTelemetryMapper;
import com.ebon.energy.fms.mapper.third.ProductMapper;
import com.ebon.energy.fms.mapper.third.SiteMapper;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import com.ebon.energy.fms.service.factory.TableStoreServiceFactory;
import com.ebon.energy.fms.util.SafeAccess;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

import static com.ebon.energy.fms.common.constants.GlobalConstants.ACCEPTABLE_IMBALANCE_WH;
import static com.ebon.energy.fms.util.SafeAccess.getValue;
import static com.ebon.energy.fms.util.TimeUtils.getDayOfTheWeekOrToday;

@Slf4j
@Service
public class SiteDashboardHistoryService {

    @Resource
    private SiteMapper siteMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private SiteDeviceService siteDeviceService;

    @Resource
    private MetricsTelemetryMapper metricsTelemetryMapper;

    @Resource
    private AddressService addressService;

    @Resource
    private UserDetailService userDetailService;

    @Resource
    private ProductService productService;

    @Resource
    private TableStoreServiceFactory tableStoreServiceFactory;

    @Resource
    private SiteConfigurationAggregatorFactory siteConfigurationAggregatorFactory;

    public SiteHistoryVO getSiteHistory(String siteId, int days) {
        Tuple2<AllAboutSiteOverview, List<DailyTotalVO>> siteHistory = getSiteNDayHistory(siteId, days);
        return getSiteSystemHistory(siteHistory._1, siteHistory._2, days);
    }

    public Tuple2<AllAboutSiteOverview, List<DailyTotalVO>> getSiteNDayHistory(String siteId, int days) {
        List<AllAboutDevice> devices = siteDeviceService.getSiteDevices(siteId);

        ISiteConfigurationAggregator siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, devices);

        int twiceDaysPlusOne = (days * 2) + 1;
        List<AllAboutDevice> siteDevices = new ArrayList<>();
        List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories = new ArrayList<>();
        for (AllAboutDevice device : devices) {
            List<DailyTotalVO> dailyTotals = productService.getNDayTotals(device.getSerialNumber(), device.getTodaysDateInLocalTime(), twiceDaysPlusOne);
            deviceHistories.add(new Tuple2<>(device, dailyTotals));
        }

        AllAboutSiteOverview allAboutSiteOverview = siteAggregator.processSiteOverview(siteId, devices);
        List<DailyTotalVO> dailyTotals = siteAggregator.processSiteNDayHistory(siteId, deviceHistories, twiceDaysPlusOne);
        return new Tuple2(allAboutSiteOverview, dailyTotals);
    }

    public SiteHistoryVO getSiteSystemHistory(
            AllAboutSiteOverview allAboutSite,
            List<DailyTotalVO> totals,
            int days) {

        if (totals == null) {
            return null;
        }

        int todaysPartialData = 1;
        int twiceDaysPlus = (days * 2) + todaysPartialData;

        Tuple2<List<DailySiteTotalsVO>, LocalDate> siteTotals = getDailySiteTotal(allAboutSite, totals, twiceDaysPlus);
        List<DailySiteTotalsVO> decendingDateOrderMerged = siteTotals._1.stream()
                .sorted(Comparator.comparing(DailySiteTotalsVO::getDate).reversed())
                .collect(Collectors.toList());
        LocalDate todaysDateInLocalTime = siteTotals._2;

        // 包含今天的部分数据 + 完整一周
        List<DailySiteTotalsVO> thisWeekWithToday = decendingDateOrderMerged.stream()
                .limit(days + todaysPartialData)
                .sorted(Comparator.comparing(DailySiteTotalsVO::getDate))
                .collect(Collectors.toList());

        // 完整一周（不包含今天）
        List<DailySiteTotalsVO> thisWeekCompleteDays = decendingDateOrderMerged.stream()
                .skip(todaysPartialData)
                .limit(days)
                .sorted(Comparator.comparing(DailySiteTotalsVO::getDate))
                .collect(Collectors.toList());

        // 上一周（不包含今天）
        List<DailySiteTotalsVO> weekBeforeCompleteDays = decendingDateOrderMerged.stream()
                .skip(todaysPartialData + days)
                .limit(days)
                .sorted(Comparator.comparing(DailySiteTotalsVO::getDate))
                .collect(Collectors.toList());

        // 电池状态校验（根据业务规则决定是否显示能耗数据）
        SevenDayHistoryVO powerUsage = null;
        if (!allAboutSite.getHasBatteries() ||
                (Optional.ofNullable(allAboutSite.getDataForDashboard())
                        .map(DataForDashboardVO::getBatterySummary)
                        .map(BatteryStatus::getStatus)
                        .map(status -> status != SystemStatusEnum.BatteryStatusValue.Disabled &&
                                status != SystemStatusEnum.BatteryStatusValue.Disconnected)
                        .orElse(true))) {
            powerUsage = getSevenDayHistoryFromDailySiteTotal(
                    allAboutSite.getPublicSiteId(),
                    thisWeekWithToday.stream()
                            .map(t -> new DayPowerPairVO(
                                    getDayOfTheWeekOrToday(t.getDate(), todaysDateInLocalTime),
                                    getValue(t.getDailyUsage(), EnergyVal::getWh, WattHour::getValue),
                                    t.getDailyUsage().getIsAccurate()))
                            .collect(Collectors.toList()),
                    thisWeekCompleteDays.stream()
                            .map(DailySiteTotalsVO::getDailyUsage)
                            .collect(Collectors.toList()),
                    weekBeforeCompleteDays.stream()
                            .map(DailySiteTotalsVO::getDailyUsage)
                            .collect(Collectors.toList()));
        }

        // 太阳能发电数据
        SevenDayHistoryVO solarProduction = getSevenDayHistoryFromDailySiteTotal(
                allAboutSite.getPublicSiteId(),
                thisWeekWithToday.stream()
                        .map(t -> new DayPowerPairVO(
                                getDayOfTheWeekOrToday(t.getDate(), todaysDateInLocalTime),
                                getValue(t.getDailyGeneration(), EnergyVal::getWh, WattHour::getValue),
                                t.getDailyGeneration().getIsAccurate()))
                        .collect(Collectors.toList()),
                thisWeekCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailyGeneration)
                        .collect(Collectors.toList()),
                weekBeforeCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailyGeneration)
                        .collect(Collectors.toList()));

        // 购电数据
        SevenDayHistoryVO bought = getSevenDayHistoryFromDailySiteTotal(
                allAboutSite.getPublicSiteId(),
                thisWeekWithToday.stream()
                        .map(t -> new DayPowerPairVO(
                                getDayOfTheWeekOrToday(t.getDate(), todaysDateInLocalTime),
                                getValue(t.getDailyBought(), EnergyVal::getWh, WattHour::getValue),
                                t.getDailyBought().getIsAccurate()))
                        .collect(Collectors.toList()),
                thisWeekCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailyBought)
                        .collect(Collectors.toList()),
                weekBeforeCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailyBought)
                        .collect(Collectors.toList()));

        // 售电数据
        SevenDayHistoryVO sold = getSevenDayHistoryFromDailySiteTotal(
                allAboutSite.getPublicSiteId(),
                thisWeekWithToday.stream()
                        .map(t -> new DayPowerPairVO(
                                getDayOfTheWeekOrToday(t.getDate(), todaysDateInLocalTime),
                                getValue(t.getDailySold(), EnergyVal::getWh, WattHour::getValue),
                                t.getDailySold().getIsAccurate()))
                        .collect(Collectors.toList()),
                thisWeekCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailySold)
                        .collect(Collectors.toList()),
                weekBeforeCompleteDays.stream()
                        .map(DailySiteTotalsVO::getDailySold)
                        .collect(Collectors.toList()));

        // 组装结果
        BoughtSoldVO boughtSold = new BoughtSoldVO(
                allAboutSite.getPublicSiteId(),
                bought,
                sold);

        return new SiteHistoryVO(powerUsage,
                solarProduction,
                boughtSold,
                allAboutSite.getIsAcCoupledMode());
    }

    private Tuple2<List<DailySiteTotalsVO>, LocalDate> getDailySiteTotal(
            AllAboutSiteOverview allAboutSite,
            List<DailyTotalVO> totals,
            int days) {
        List<DailySiteTotalsVO> potentiallyIncompleteDailyTotalsRaw = totals.stream()
                .map(DailyTotalVO::asPublicDto)
                .collect(Collectors.toList());

        // 处理能源平衡计算
        List<DailySiteTotalsVO> potentiallyIncompleteDailyTotals = potentiallyIncompleteDailyTotalsRaw.stream()
                .map(history -> {
                    // 判断是否支持负载贡献者
                    boolean supportsLoadContributors = allAboutSite.getSupportsLoadContributorsSince() != null &&
                            (history.getDate().isAfter(allAboutSite.getSupportsLoadContributorsSince()) ||
                                    history.getDate().isEqual(allAboutSite.getSupportsLoadContributorsSince()));

                    // 获取设备详情（假设至少有一个设备）
                    AllAboutDevice firstDevice = allAboutSite.getDeviceDetails().stream()
                            .findFirst()
                            .orElseThrow(() -> new BizException("No device details found"));

                    // 能源平衡计算
                    EnergyBalancingInputs inputs = new EnergyBalancingInputs(
                            history.getDailyUsage().getWh(),
                            history.getDailySold().getWh(),
                            history.getDailyBought().getWh(),
                            history.getDailyGeneration().getWh(),
                            getValue(history.getDailyBatteryCharged(), EnergyVal::getWh),
                            getValue(history.getDailyBatteryDischarged(), EnergyVal::getWh),
                            allAboutSite.getMaximumPossiblePVOutput(),
                            firstDevice.getFirstSiteDevice().getEnergyBalancingMethod(),
                            new WattHour(ACCEPTABLE_IMBALANCE_WH.multiply(BigDecimal.valueOf(allAboutSite.getDeviceDetails().size()))),
                            supportsLoadContributors
                    );

                    Energies energies = EnergyBalancer.getEnergies(inputs);

                    // 处理电池充放电（可空值）
                    Optional<EnergyVal> dailyBatteryCharged = Optional.ofNullable(energies.getBatteryChargedWh())
                            .map(wh -> new EnergyVal(wh.asInt(),
                                    SafeAccess.safeGet(history.getDailyBatteryCharged(), EnergyVal::getIsAccurate).orElse(false)));

                    Optional<EnergyVal> dailyBatteryDischarged = Optional.ofNullable(energies.getBatteryDischargedWh())
                            .map(wh -> new EnergyVal(wh.asInt(),
                                    SafeAccess.safeGet(history.getDailyBatteryDischarged(), EnergyVal::getIsAccurate).orElse(false)));

                    return new DailySiteTotalsVO(
                            history.getDate(),
                            new EnergyVal(energies.getUsageWh().asInt(), history.getDailyUsage().getIsAccurate()),
                            new EnergyVal(energies.getSoldWh().asInt(), history.getDailySold().getIsAccurate()),
                            new EnergyVal(energies.getBoughtWh().asInt(), history.getDailyBought().getIsAccurate()),
                            new EnergyVal(SafeAccess.safeGet(energies.getGenerationWh(), WattHour::asInt).orElse(0),
                                    history.getDailyGeneration().getIsAccurate()),
                            dailyBatteryCharged.orElse(null),
                            dailyBatteryDischarged.orElse(null)
                    );
                })
                .collect(Collectors.toList());

        // 创建完整的日期范围（降序）
        List<DailySiteTotalsVO> completeDecendingDateOrderDailyTotalEmptySlots = IntStream.range(0, days)
                .mapToObj(i -> allAboutSite.getTodaysDateInLocalTime().minusDays(days - 1 - i))
                .map(date -> DailySiteTotalsVO.empty(date))
                .sorted(Comparator.comparing(DailySiteTotalsVO::getDate).reversed())
                .collect(Collectors.toList());

        // 合并数据
        List<DailySiteTotalsVO> decendingDateOrderMerged = getMergedTotals(
                potentiallyIncompleteDailyTotals,
                completeDecendingDateOrderDailyTotalEmptySlots
        );

        // 返回结果对
        return new Tuple2(decendingDateOrderMerged, allAboutSite.getTodaysDateInLocalTime());
    }

    private List<DailySiteTotalsVO> getMergedTotals(
            List<DailySiteTotalsVO> potentiallyIncompleteDailyTotals,
            Iterable<DailySiteTotalsVO> slots) {

        // 创建日期到DailySiteTotalsDto的映射
        Map<LocalDate, DailySiteTotalsVO> map = new HashMap<>();
        potentiallyIncompleteDailyTotals.forEach(t -> map.put(t.getDate(), t));

        // 遍历slots，按日期从map中获取已存在的记录，否则使用slot本身
        return StreamSupport.stream(slots.spliterator(), false)
                .map(slot -> map.getOrDefault(slot.getDate(), slot))
                .collect(Collectors.toList());
    }

    private SevenDayHistoryVO getSevenDayHistoryFromDailySiteTotal(
            String publicSiteId,
            List<DayPowerPairVO> thisWeekWithToday,
            List<EnergyVal> thisWeekCompleteDays,
            List<EnergyVal> weekBeforeCompleteDays) {

        Objects.requireNonNull(publicSiteId, "publicSiteId must not be null");
        Objects.requireNonNull(thisWeekWithToday, "thisWeekWithToday must not be null");
        Objects.requireNonNull(thisWeekCompleteDays, "thisWeekCompleteDays must not be null");
        Objects.requireNonNull(weekBeforeCompleteDays, "weekBeforeCompleteDays must not be null");

        // 计算本周和上周的能耗总和（保留两位小数）
        BigDecimal thisWeekUsageSumWh = calculateTotalWh(thisWeekCompleteDays);
        BigDecimal priorWeekUsageSumWh = calculateTotalWh(weekBeforeCompleteDays);

        BigDecimal weekDiffUsageNegativeIsLess = BigDecimal.ZERO;
        if (priorWeekUsageSumWh.compareTo(BigDecimal.ZERO) > 0) {
            // 计算百分比变化：(本周 - 上周) / 上周 * 100
            BigDecimal difference = thisWeekUsageSumWh.subtract(priorWeekUsageSumWh);
            if (difference.compareTo(BigDecimal.ZERO) != 0) {
                weekDiffUsageNegativeIsLess = difference.divide(priorWeekUsageSumWh, 10, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP).stripTrailingZeros();
            }
        }

        // 转换为不可变列表（假设DayPowerPairDto是不可变的）
        List<DayPowerPairVO> immutableData = thisWeekWithToday.stream()
                .collect(Collectors.toUnmodifiableList());

        return new SevenDayHistoryVO(
                publicSiteId,
                thisWeekUsageSumWh,
                weekDiffUsageNegativeIsLess.intValue(),
                immutableData,
                true // applyChartSafeDataFeature 固定为true
        );
    }

    private BigDecimal calculateTotalWh(List<EnergyVal> energyVals) {
        return energyVals.stream()
                .map(e -> e.getWh().getValue())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
