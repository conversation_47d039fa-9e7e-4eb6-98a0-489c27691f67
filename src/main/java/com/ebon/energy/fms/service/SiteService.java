package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.UserPreferenceKey;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.SiteCoordinatorHelper;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.po.SiteInverterAddPO;
import com.ebon.energy.fms.domain.po.SiteInverterUpdatePO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.site.DecideCoordinatorDto;
import com.ebon.energy.fms.domain.vo.site.SetSiteCoordinatorDataInDto;
import com.ebon.energy.fms.domain.vo.site.SetSiteCoordinatorDataOutDto;
import com.ebon.energy.fms.domain.vo.site.SiteCoordinatorSiteData;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.mapper.third.*;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.service.factory.TableStoreServiceFactory;
import com.ebon.energy.fms.util.*;
import com.google.common.base.Strings;
import com.microsoft.sqlserver.jdbc.SQLServerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.common.constants.GlobalConstants.ROLE_MASTER;
import static com.ebon.energy.fms.common.utils.ProductUtilities.isOnline;
import static com.ebon.energy.fms.util.SafeAccess.getValue;
import static com.ebon.energy.fms.util.StreamUtil.mapList;
import static com.ebon.energy.fms.util.StreamUtil.toMap;

@Slf4j
@Service
public class SiteService {

    @Resource
    private SiteMapper siteMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductInstallationMapper productInstallationMapper;

    @Resource
    private MetricsTelemetryMapper metricsTelemetryMapper;

    @Resource
    private HardwareModelMapper hardwareModelMapper;

    @Resource
    private ErrorMappingMapper errorMappingMapper;

    @Resource
    private ProductDbRepository productDbRepository;

    @Resource
    private AddressService addressService;

    @Resource
    private UserDetailService userDetailService;

    @Resource
    private SiteDeviceService siteDeviceService;

    @Resource
    private TableStoreServiceFactory tableStoreServiceFactory;

    public SiteDO getBySystemId(String systemId) {
        LambdaQueryWrapper<SiteDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteDO::getSystemId, systemId);
        return siteMapper.selectOne(queryWrapper);
    }

    public SiteVO getSiteDetail(String siteId) {
        SiteVO siteVO = new SiteVO();
        siteVO.setSiteId(siteId);

        SiteDO siteDO = getBySystemId(siteId);
        AddressesDO addressDO = addressService.getById(siteDO.getAddressId());
        siteVO.setAddress(AddressFormatter.formatAddress((addressDO)));

        List<SiteInverterInfoDO> inverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isNotEmpty(inverters)) {
            boolean isAllOnline = inverters.stream().allMatch(e -> isOnline(e.getLatestTelemetryUtc(), 600000));
            if (isAllOnline) {
                siteVO.setOnlineStatus(3);
            } else {
                boolean isAllOffline = inverters.stream().allMatch(e -> !isOnline(e.getLatestTelemetryUtc(), 600000));
                if (isAllOffline) {
                    siteVO.setOnlineStatus(1);
                } else {
                    siteVO.setOnlineStatus(2);
                }
            }
        } else {
            siteVO.setOnlineStatus(0);
        }

        List<SiteDeviceDO> siteDevices = siteMapper.selectSiteDevices(siteId);
        if (CollectionUtils.isNotEmpty(siteDevices)) {
            ProductWithOwnerDO ownerDO = productMapper.selectWithOwner(siteDevices.get(0).getSerialNumber());
            if (ownerDO != null) {
                siteVO.setOwnerName(Strings.nullToEmpty(ownerDO.getOwnerFirstName()) + " " + Strings.nullToEmpty(ownerDO.getOwnerLastName()));
                siteVO.setOwnerName(siteVO.getOwnerName().trim());
            }

            List<ErrorVO> errors = new ArrayList<>();
            for (SiteDeviceDO siteDevice : siteDevices) {
                SystemStatus systemStatus = JSONObject.parseObject(siteDevice.getLatestSystemStatus(), SystemStatus.class);
                if (systemStatus != null && CollectionUtils.isNotEmpty(systemStatus.getSystemStatusErrors())) {
                    List<ErrorVO> errs = mapList(systemStatus.getSystemStatusErrors(), e -> {
                        ErrorVO errorVO = new ErrorVO();
                        BeanUtils.copyProperties(e, errorVO);
                        errorVO.setSerialNumber(siteDevice.getSerialNumber());
                        return errorVO;
                    });
                    errors.addAll(errs);
                }
            }

            List<ErrorMappingDO> errorMappings = errorMappingMapper.selectList(new QueryWrapper<>());
            Map<Integer, ErrorMappingDO> allErrorMap = toMap(errorMappings, ErrorMappingDO::getId);
            if (CollectionUtils.isNotEmpty(errors)) {
                errors.forEach(e -> {
                    ErrorMappingDO errorMappingDO = allErrorMap.get(e.getErrorCode());
                    if (errorMappingDO != null) {
                        e.setErrorDescription(errorMappingDO.getDescription());
                    }
                });
            }

            siteVO.setErrors(errors);
        }

        return siteVO;
    }

    public PageResult<SiteVO> getSiteList(SiteListPO po) {
        Page<SiteVO> page = new Page<>(po.getCurrent(), po.getPageSize());
        Page<SiteVO> sitePage = siteMapper.selectSitePage(page, po);
        if (sitePage.getTotal() == 0) {
            return PageResult.toResponse(Collections.EMPTY_LIST, sitePage.getTotal(), po.getCurrent(), po.getPageSize());
        }

        return PageResult.toResponse(sitePage.getRecords(), sitePage.getTotal(), po.getCurrent(), po.getPageSize());
    }

    public List<SiteInverterInfoVO> getSiteInverterList(String siteId) {
        List<SiteInverterInfoDO> inverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(inverters)) {
            return Collections.EMPTY_LIST;
        }

        List<HardwareModelAndFamilyDO> hardwareModels = hardwareModelMapper.selectWithFamily();
        Map<String, HardwareModelAndFamilyDO> hardwareModelMap = hardwareModels.stream().collect(Collectors.toMap(HardwareModelAndFamilyDO::getName, Function.identity(), (first, second) -> first));

        List<String> sns = mapList(inverters, SiteInverterInfoDO::getSerialNumber);
        List<RedbackUserDetailsDO> userDetails = userDetailService.getUserDetails(sns, UserPreferenceKey.ProductFriendlyName.name());
        Map<String, String> userDetailMap = StreamUtil.toMap(userDetails, RedbackUserDetailsDO::getRedbackProductSn, RedbackUserDetailsDO::getValue);

        List<MetricsTelemetryDO> metricsList = metricsTelemetryMapper.selectInSns(sns);
        Map<String, MetricsTelemetryDO> metricsMap = metricsList.stream().collect(Collectors.toMap(MetricsTelemetryDO::getSerialNumber, Function.identity(), (first, second) -> first));
        List<SiteInverterInfoVO> inverterList = inverters.stream().map(e -> {
            SiteInverterInfoVO inverterVO = new SiteInverterInfoVO();

            MetricsTelemetryDO telemetryDO = metricsMap.get(e.getSerialNumber());
            boolean hasError = telemetryDO.getIncidentsForHomeUser() > 0 || telemetryDO.getIncidentsForInstaller() > 0 || telemetryDO.getIncidentsForRb() > 0;

            inverterVO.setInverterName(userDetailMap.get(e.getSerialNumber()));
            inverterVO.setCurrentRole(decideCurrentRole(e.getCurrentRole(), e.getSelectedRole(),
                    new RossVersion(e.getRossVersion()).getRossVersionNumber(), e.getModel()));
            inverterVO.setCurrentRoleName(PhaseRole.getDisplayNameFromName(inverterVO.getCurrentRole()));
            inverterVO.setSerialNumber(e.getSerialNumber());
            inverterVO.setMeter(e.getMeter() != null && "TRUE".equalsIgnoreCase(e.getMeter()) ? "Connected" : "");

            String modelName = e.getModel();
            HardwareModelAndFamilyDO hardwareModelDO = modelName != null ? hardwareModelMap.get(modelName) : null;
            inverterVO.setModel(Strings.nullToEmpty(hardwareModelDO != null ? hardwareModelDO.getDisplayName() : modelName));

            inverterVO.setSelectedRole(e.getSelectedRole());
            inverterVO.setSelectedRoleName(PhaseRole.getDisplayNameFromName(inverterVO.getSelectedRole()));
            inverterVO.setStatus(InverterStatusHelper.makeInverterStatus(
                    e.isInWarranty(),
                    e.isOffComms(),
                    e.isRegistered(),
                    e.isPending(),
                    hasError,
                    e.isOnline()));
            inverterVO.setRossVersion(e.getRossVersion());
            inverterVO.setFirmwareVersion(e.getFirmwareVersion());
            inverterVO.setSiteId(e.getPublicSiteId());
            inverterVO.setComputerName(e.getComputerName());
            inverterVO.setLatestTelemetryUtc(TimeUtils.toZonedDateTime(e.getLatestTelemetryUtc()));
            inverterVO.setIsHourlyOnline(isOnline(e.getLatestTelemetryUtc(), 3600000L));
            inverterVO.setIsDailyOnline(isOnline(e.getLatestTelemetryUtc(), 86400000L));

            setComputerName(inverterVO);

            return inverterVO;
        }).collect(Collectors.toList());

        return sortResults(inverterList);
    }

    public void updateInverterSelectedRole(SiteInverterUpdatePO updatePO) {
        PhaseRole.valueOf(updatePO.getSelectedRole());
        
        String siteId = siteMapper.selectSiteId(updatePO.getSerialNumber());
        List<SiteInverterInfoDO> siteInverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(siteInverters)) {
            throw new BizException("There are no device");
        }

        SiteInverterInfoDO inverterInfoDO = siteInverters.stream().filter(e -> e.getSerialNumber().equals(updatePO.getSerialNumber())).findFirst().orElse(null);
        if (!ObjectUtils.equals(inverterInfoDO.getSelectedRole(), updatePO.getSelectedRole())) {
            List<SiteInverterInfoDO> updateList = new ArrayList<>();
            if (updatePO.getSelectedRole().equals(PhaseRole.Coordinator.name())) {
                siteInverters.stream().filter(e -> e.getSelectedRole().equalsIgnoreCase(PhaseRole.Coordinator.name())).forEach(e -> {
                    e.setSelectedRole(PhaseRole.Worker.name());
                    updateList.add(e);
                });
            }

            inverterInfoDO.setSelectedRole(updatePO.getSelectedRole());
            updateList.add(inverterInfoDO);

            List<SiteInverterInfoVO> vos = mapList(siteInverters, e -> {
                SiteInverterInfoVO vo = new SiteInverterInfoVO();
                BeanUtils.copyProperties(e, vo);
                vo.setCurrentRole(decideCurrentRole(e.getCurrentRole(), e.getSelectedRole(),
                        new RossVersion(e.getRossVersion()).getRossVersionNumber(), e.getModel()));
                return vo;
            });

            List<SiteInverterInfoVO> inverters = sortResults(vos);

            long count = inverters.stream().filter(e -> e.getSelectedRole().equalsIgnoreCase(PhaseRole.Coordinator.name())).count();
            if (count != 1) {
                throw new BizException("There must be one " + PhaseRole.Coordinator.getDisplayName() + ". There are currently " + count + ".");
            }

            // 筛选出所有角色为Worker且不支持Site Coordinator的设备
            List<SiteInverterInfoVO> unsupportedWorkers = inverters.stream()
                    .filter(d -> {
                        RossVersion rossVersion = new RossVersion(d.getRossVersion());
                        return PhaseRole.Worker.name().equals(d.getSelectedRole())
                                && !rossVersion.supportsSiteCoordinator();
                    })
                    .collect(Collectors.toList());

            // 如果存在不支持的Worker设备
            if (CollectionUtils.isNotEmpty(unsupportedWorkers)) {
                // 检查是否同时存在支持的Worker设备
                boolean hasSupportedWorkers = inverters.stream()
                        .anyMatch(d -> {
                            RossVersion rossVersion = new RossVersion(d.getRossVersion());
                            return PhaseRole.Worker.name().equals(d.getSelectedRole())
                                    && rossVersion.supportsSiteCoordinator();
                        });
                if (!hasSupportedWorkers) {
                    throw new BizException("All devices entering a Master/Worker relationship, must first be updated to a version that supports site manager.");
                }
            }

            for (SiteInverterInfoDO updateDO : updateList) {
                productInstallationMapper.updatePhaseRole(updateDO.getSerialNumber(), updateDO.getSelectedRole());
            }

            siteDeviceService.updateSettingsAndNotify(inverters);
        }

        //更新逆变器名称
        RedbackUserDetailsDO detailsDO = new RedbackUserDetailsDO();
        detailsDO.setRedbackUserId(RequestUtil.getPortolUserId());
        detailsDO.setRedbackProductSn(updatePO.getSerialNumber());
        detailsDO.setName(UserPreferenceKey.ProductFriendlyName.name());
        detailsDO.setValue(updatePO.getInverterName());
        userDetailService.save(detailsDO);
    }

    public void addInverter(SiteInverterAddPO addPO) {
        int cnt = productInstallationMapper.countBySiteIdAndSn(addPO.getSiteId(), addPO.getSerialNumber());
        if (cnt > 0) {
            throw new BizException("Product belongs to the site already");
        }

        RedbackProductInstallationDO productInstallationDO = productInstallationMapper.selectBySn(addPO.getSerialNumber());
        if (productInstallationDO == null) {
            throw new BizException("Product installation info is not exist");
        }

        try {
            productInstallationMapper.mergeProductToSite(addPO.getSiteId(), addPO.getSerialNumber());
        } catch (UncategorizedSQLException ex) {
            SQLException sqlException = ex.getSQLException();
            if (sqlException instanceof SQLServerException) {
                SQLServerException sqlServerException = (SQLServerException) sqlException;
                String errorMessage = sqlServerException.getMessage();
                int errorCode = sqlServerException.getErrorCode();
                throw new BizException(errorMessage);
            }

            throw ex;
        }
      
        setSiteRoleBySerialNumber(addPO.getSerialNumber());

        String siteId = siteMapper.selectSiteId(addPO.getSerialNumber());
        List<SiteInverterInfoDO> siteInverters = productMapper.selectSiteInverters(siteId);
        if (CollectionUtils.isEmpty(siteInverters)) {
            throw new BizException("There are no device");
        }

        List<SiteInverterInfoVO> vos = mapList(siteInverters, e -> {
            SiteInverterInfoVO vo = new SiteInverterInfoVO();
            BeanUtils.copyProperties(e, vo);
            vo.setCurrentRole(decideCurrentRole(e.getCurrentRole(), e.getSelectedRole(),
                    new RossVersion(e.getRossVersion()).getRossVersionNumber(), e.getModel()));
            return vo;
        });
        List<SiteInverterInfoVO> inverters = sortResults(vos);

        siteDeviceService.updateSettingsAndNotify(inverters);
    }

    public void setSiteRoleBySerialNumber(String serialNumber) {
        List<SiteCoordinatorSiteDataDO> siteDatas = siteMapper.selectSiteDataBySerialNumber(serialNumber);
        List<SiteCoordinatorSiteData> siteDataList = new ArrayList<>();
        for (SiteCoordinatorSiteDataDO siteDataDO : siteDatas) {
            LocalDateTime mDate = null;
            if (siteDataDO.getManufactureDate().length() == 8) {
                try {
                    String year = siteDataDO.getManufactureDate().substring(0, 4);
                    String month = siteDataDO.getManufactureDate().substring(4, 6);
                    String day = siteDataDO.getManufactureDate().substring(6, 8);
                    String dateStr = year + "-" + month + "-" + day;

                    mDate = LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
                } catch (DateTimeParseException e) {
                }
            }

            SiteCoordinatorSiteData siteData = new SiteCoordinatorSiteData();
            BeanUtils.copyProperties(siteDataDO, siteData);
            siteData.setRossVersion(new RossVersion(siteDataDO.getVersion()).getRossVersionNumber());
            siteData.setIsConnected(siteDataDO.getIsConnected() != null ? siteDataDO.getIsConnected() : false);
            siteData.setManufactureDate(mDate != null ? mDate : LocalDateTime.now());
            siteDataList.add(siteData);
        }

        String coordinator = SiteCoordinatorHelper.decideCoordinator(siteDataList.stream().map(e -> {
            DecideCoordinatorDto coordinatorDto = new DecideCoordinatorDto();
            coordinatorDto.setRossSerialNumber(e.getSerialNumber());
            coordinatorDto.setIsConnected(e.getIsConnected());
            coordinatorDto.setRossVersion(e.getRossVersion());
            coordinatorDto.setModel(e.getModel());
            coordinatorDto.setManufactureDate(e.getManufactureDate());
            coordinatorDto.setManufactureSerialNumber(e.getManufactureSerialNumber());
            coordinatorDto.setSiteRole(e.getSiteRole());
            coordinatorDto.setIsGridTie(e.getIsGridTie());
            return coordinatorDto;
        }).collect(Collectors.toList()));

        List<SetSiteCoordinatorDataInDto> newRoles = siteDataList.stream()
                .map(p -> {
                    SetSiteCoordinatorDataInDto dto = new SetSiteCoordinatorDataInDto();
                    dto.setSerialNumber(p.getSerialNumber());

                    String role = p.getSerialNumber().equals(coordinator)
                            ? PhaseRole.Coordinator.name()
                            : SiteCoordinatorHelper.canBeCoordinatorOrWorker(
                            p.getIsConnected() != null ? p.getIsConnected() : false,
                            p.getRossVersion(),
                            p.getIsGridTie())
                            ? PhaseRole.Worker.name()
                            : p.getIsGridTie()
                            ? PhaseRole.GridTie.name()
                            : PhaseRole.Incompatible.name();

                    dto.setRole(role);
                    return dto;
                })
                .collect(Collectors.toList());

        String validationError = validateSiteCoordinator(newRoles);
        if (StringUtils.isNotBlank(validationError)) {
            throw new BizException(validationError);
        }

        for (SetSiteCoordinatorDataInDto newRole : newRoles) {
            productInstallationMapper.updatePhaseRole(newRole.getSerialNumber(), newRole.getRole());
        }
    }

    private String validateSiteCoordinator(List<SetSiteCoordinatorDataInDto> siteData) {
        List<String> sns = mapList(siteData, SetSiteCoordinatorDataInDto::getSerialNumber);
        List<InverterRoleSiteIdDO> validateData = productDbRepository.getSiteInfoByProducts(sns);

        // 检查站点ID是否唯一
        long distinctSiteCount = validateData.stream()
                .map(InverterRoleSiteIdDO::getSiteId)
                .distinct()
                .count();

        if (distinctSiteCount > 1) {
            return "You cannot update products from different sites";
        }

        List<InverterRoleSiteIdDO> validateDataToUpdate = new ArrayList<>(validateData);

        // 设置新角色
        siteData.forEach(sdata -> {
            InverterRoleSiteIdDO target = validateDataToUpdate.stream()
                    .filter(p -> p.getSerialNumber().equals(sdata.getSerialNumber()))
                    .findFirst()
                    .orElse(null);
            if (target != null) {
                target.setRole(sdata.getRole());
            }
        });

        // 处理版本和设备ID
        List<SetSiteCoordinatorDataOutDto> serialNumberDevices = new ArrayList<>();
        validateDataToUpdate.forEach(vdata -> {
            InverterRoleSiteIdDO target = validateDataToUpdate.stream()
                    .filter(p -> p.getSerialNumber().equals(vdata.getSerialNumber()))
                    .findFirst()
                    .orElse(null);
            if (target != null) {
                target.setVersionObject(new RossVersion(vdata.getVersion()).getRossVersionNumber());
                if (!Objects.isNull(vdata.getDeviceId())) {
                    SetSiteCoordinatorDataOutDto setSiteCoordinatorDataOutDto = new SetSiteCoordinatorDataOutDto();
                    setSiteCoordinatorDataOutDto.setSerialNumber(vdata.getSerialNumber());
                    setSiteCoordinatorDataOutDto.setDeviceId(vdata.getDeviceId());
                    setSiteCoordinatorDataOutDto.setRole(vdata.getRole());
                    serialNumberDevices.add(setSiteCoordinatorDataOutDto);
                }
            }
        });

        // 检查协调器数量
        long coordinatorCount = validateDataToUpdate.stream()
                .filter(p -> p.getRole().equals(PhaseRole.Coordinator.name()))
                .count();

        if (coordinatorCount > 1) {
            return "You can only have one Coordinator per site";
        }

        if (coordinatorCount == 0) {
            return "You cannot have a site without Coordinator";
        }

        return "";
    }

    private void setComputerName(SiteInverterInfoVO item) {
        String computerName = item.getComputerName();
        if (StringUtils.isBlank(computerName)) {
            if (item.getLatestTelemetryUtc() != null && StringUtils.isNotEmpty(item.getSerialNumber())) {
                String serialNumber = item.getSerialNumber();
                long epoch = item.getLatestTelemetryUtc().toEpochSecond();

                TelemetryDataVO telemetryData = tableStoreServiceFactory.getService(CloudPlatformName.Tuya).getTelemetryData(serialNumber, epoch);
                String telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);

                if (StringUtils.isBlank(telemetry)) {
                    telemetryData = tableStoreServiceFactory.getService(CloudPlatformName.Azure).getTelemetryData(serialNumber, epoch);
                    telemetry = getValue(telemetryData, TelemetryDataVO::getTelemetry);
                }

                if (StringUtils.isNoneBlank(telemetry)) {
                    RossTelemetry rossTelemetry = JSONObject.parseObject(telemetry, RossTelemetry.class);
                    if (rossTelemetry != null) {
                        TelemetryExtensions.rebuildDataBackedBands(rossTelemetry.getRossDeviceTelemetry());
                        computerName = getValue(rossTelemetry, RossTelemetry::getRossDeviceTelemetry, RossDeviceTelemetry::getOuijaBoard, OuijaBoardTelemetry::getComputerName);
                    }
                }
            }
        }

        item.setComputerName(computerName);
    }

    private String decideCurrentRole(String currentRole, String selectedRole,
                                     Version version, String model) {
        if (version != null && version.getMajor() >= 2 && version.getMinor() >= 17
                && !model.toLowerCase().startsWith("si")) {
            return currentRole;
        }

        return selectedRole;
    }

    private List<SiteInverterInfoVO> sortResults(List<SiteInverterInfoVO> result) {
        return result.stream()
                .sorted(Comparator
                        .comparingInt((SiteInverterInfoVO d) -> getRoleOrder(d.getCurrentRole()))
                        .thenComparingInt((SiteInverterInfoVO d) -> getRoleOrder(d.getSelectedRole()))
                        .thenComparing((SiteInverterInfoVO d) -> d.getSerialNumber()))
                .collect(Collectors.toList());
    }

    private int getRoleOrder(String role) {
        if (role == null) {
            return 3;
        }
        if (role.equals(PhaseRole.Coordinator.name()) || role.equals(ROLE_MASTER)) {
            return 1;
        }
        if (role.equals(PhaseRole.Worker.name())) {
            return 2;
        }
        return 3;
    }
}
