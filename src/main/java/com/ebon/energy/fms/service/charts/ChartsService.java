package com.ebon.energy.fms.service.charts;

import com.ebon.energy.fms.common.enums.SystemStatusEnum;
import com.ebon.energy.fms.common.utils.ChartsUtility;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.controller.request.ChartsPlotOptionsRequest;
import com.ebon.energy.fms.domain.vo.charts.ChartsDataBandVO;
import com.ebon.energy.fms.domain.vo.charts.ChartsSystemStatusViewModel;
import com.ebon.energy.fms.domain.vo.charts.ChartsViewModel;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.vo.product.control.RossVersion;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.repository.IRedbackJobHistoryRepository;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.service.TelemetryService;
import com.ebon.energy.fms.util.ConfigurationUtil;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChartsService {

    private final RedbackRepository repository;

    private final ProductDbRepository productDbRepository;

    private final SpecificationRepository specificationRepository;

    private final IRedbackJobHistoryRepository redbackJobHistoryRepository;

    /**
     * 获取图表配置信息
     *
     * @param sn 设备序列号
     * @return ChartsViewModel 图表视图模型
     */
    public ChartsViewModel getCharts(String sn) {
        var maxDaysDateRange = ConfigurationUtil.getPortalConfig().getMaxDaysDateRange();
        try {
            // 获取系统状态
            var systemStatus = repository.getLatestSystemStatusAsync(sn);
            // TODO: Nicer error when no system status exists yet (ie new setup / test inverter).
            // 处理没有遥测的情况
            if (systemStatus == null) {
                return new ChartsViewModel(
                        null,
                        0,
                        0,
                        Collections.emptyList(),
                        maxDaysDateRange,
                        false,
                        false,
                        false,
                        false
                );
            }

            // 检查系统状态是否知道有电池
            boolean hasBattery = systemStatus.getBattery() != null ||
                    (systemStatus.getSystemStatusErrors() != null &&
                            systemStatus.getSystemStatusErrors().stream()
                                    .anyMatch(ss -> ss.getType() == SystemStatusEnum.SystemStatusError.Battery));

            int batteryCount = systemStatus.getBattery() != null && systemStatus.getBattery().getBatteries() != null
                    ? systemStatus.getBattery().getBatteries().size()
                    : 0;

            int pvCount = systemStatus.getPV() != null && systemStatus.getPV().getPVs() != null
                    ? systemStatus.getPV().getPVs().size()
                    : 0;

            int relayCount = systemStatus.getRelays() != null
                    ? systemStatus.getRelays().size()
                    : 0;

            RossVersion rossVersion = new RossVersion(
                    systemStatus.getOuijaBoard() != null ? systemStatus.getOuijaBoard().getSoftwareVersion() : null
            );

            InstallationSpecification specification = specificationRepository.getInstallationSpecAsync(sn);
            boolean measuringThirdPartyInverter = specification.isSmartBatteryInverter() || specification.isInAcCoupledMode();

            // 硬编码的电池最小值
            int minBatteries = specification.isSmartBatteryInverter() ? 3 : 4;

            if (batteryCount < minBatteries) {
                batteryCount = minBatteries;
            }

            // 如果支持 PV，硬编码最少一个 PV 字符串
            int minPvCount = specification.isSupportsConnectedPV() ? 1 : 0;
            pvCount = Math.max(pvCount, minPvCount);

            return new ChartsViewModel(
                    systemStatus.getOuijaBoard() != null ? systemStatus.getOuijaBoard().getTimeZone() : null,
                    batteryCount,
                    pvCount,
                    systemStatus.getRelays() != null ? systemStatus.getRelays() : Collections.emptyList(),
                    maxDaysDateRange,
                    hasBattery,
                    specification.isThreePhaseInverter(),
                    measuringThirdPartyInverter,
                    specification.isGridTieInverter()
            );

        } catch (Exception e) {
            log.error("获取图表配置失败，序列号: {}", sn, e);
            // 返回默认配置
            return new ChartsViewModel(
                    null,
                    0,
                    0,
                    Collections.emptyList(),
                    maxDaysDateRange,
                    false,
                    false,
                    false,
                    false
            );
        }
    }

    public ChartsDataBandVO dataBand(ChartsPlotOptionsRequest options) {
        var portolUserId = RequestUtil.getPortolUserId();
        var maxDaysDateRange = ConfigurationUtil.getPortalConfig().getMaxDaysDateRange();
        var dateRange = ChartsUtility.getStartAndEndDate(options.getStartDate(), options.getEndDate(), options.getTimeZone());
        if (maxDaysDateRange >= 0)
        {
            int secondsInDay = 86400;
            int maxNumSecondsInRange = secondsInDay * maxDaysDateRange;

            // check if request is for too long a time period
            if (dateRange.getEnd() > dateRange.getStart() && dateRange.getEnd() - dateRange.getStart() >= maxNumSecondsInRange)
            {
                // shorten the date range
                dateRange.setEnd(dateRange.getStart() +  maxNumSecondsInRange);
            }
        }

        var reductionJobs = redbackJobHistoryRepository.getStorageReductionJobsAsync();
        var bridgeGapSize = ChartsUtility.getEpochBridgeGapSize(dateRange.getStart(), dateRange.getEnd(), reductionJobs);

        List<SystemStatus> queryResult = productDbRepository.getSystemStatusesInRangeAsync(options.getSerialNumber(), dateRange.getStart(), dateRange.getEnd());

        // 获取时区映射
        ZoneId timeZone = null;
        if (options.getTimeZone() != null) {
            timeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(options.getTimeZone());
        }

        // 创建ROSS版本带数据
        List<ChartsDataBandVO.BandItemVO> rossVersionBands = queryResult.stream()
                .map(m -> new Object() {
                    String softwareVersion = Optional.ofNullable(m.getOuijaBoard())
                            .map(OuijaBoard::getSoftwareVersion)
                            .orElse(null);
                    Long epoch = m.getEpoch();
                })
                .filter(item -> item.softwareVersion != null)
                .collect(Collectors.groupingBy(
                        item -> item.softwareVersion,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    Long minEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .min()
                                            .orElse(0L);
                                    Long maxEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .max()
                                            .orElse(0L);
                                    return new ChartsDataBandVO.BandItemVO(list.get(0).softwareVersion, minEpoch, maxEpoch);
                                }
                        )
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 创建固件版本带数据
        List<ChartsDataBandVO.BandItemVO> firmwareVersionBands = queryResult.stream()
                .map(m -> new Object() {
                    String firmwareVersion = Optional.ofNullable(m.getInverter())
                            .map(InverterStatus::getFirmwareVersion)
                            .orElse(null);
                    Long epoch = m.getEpoch();
                })
                .filter(item -> item.firmwareVersion != null)
                .collect(Collectors.groupingBy(
                        item -> item.firmwareVersion,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    Long minEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .min()
                                            .orElse(0L);
                                    Long maxEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .max()
                                            .orElse(0L);
                                    return new ChartsDataBandVO.BandItemVO("Firmware " + list.get(0).firmwareVersion, minEpoch, maxEpoch);
                                }
                        )
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 创建ROSS固件版本组合带数据
        List<ChartsDataBandVO.BandItemVO> rossFirmwareVersionBands = queryResult.stream()
                .map(m -> new Object() {
                    String firmwareVersion = Optional.ofNullable(m.getInverter())
                            .map(InverterStatus::getFirmwareVersion)
                            .orElse(null);
                    String softwareVersion = Optional.ofNullable(m.getOuijaBoard())
                            .map(OuijaBoard::getSoftwareVersion)
                            .orElse(null);
                    Long epoch = m.getEpoch();
                    String combinedKey = firmwareVersion + "|" + softwareVersion;
                })
                .filter(item -> item.firmwareVersion != null && item.softwareVersion != null)
                .collect(Collectors.groupingBy(
                        item -> item.combinedKey,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    Long minEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .min()
                                            .orElse(0L);
                                    Long maxEpoch = list.stream()
                                            .mapToLong(item -> item.epoch)
                                            .max()
                                            .orElse(0L);
                                    String displayName = list.get(0).softwareVersion + "<br>Firmware v" + list.get(0).firmwareVersion;
                                    return new ChartsDataBandVO.BandItemVO(displayName, minEpoch, maxEpoch);
                                }
                        )
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 构建Bands对象
        ChartsDataBandVO.BandsVO bands = ChartsDataBandVO.BandsVO.builder()
                .rossVersion(rossVersionBands)
                .firmwareVersion(firmwareVersionBands)
                .rossFirmwareVersion(rossFirmwareVersionBands)
                .build();

        // 转换PlotPoints
        List<ChartsSystemStatusViewModel> plotPoints = queryResult.stream()
                .map(ChartsSystemStatusViewModel::new)
                .collect(Collectors.toList());

        // 构建最终数据
        return ChartsDataBandVO.builder()
                .bands(bands)
                .plotPoints(plotPoints)
                .timezone(timeZone != null ? timeZone.getId() : null)
                .bridgeGapSize(bridgeGapSize)
                .build();

    }
}
