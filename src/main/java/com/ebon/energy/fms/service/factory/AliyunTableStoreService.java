package com.ebon.energy.fms.service.factory;

import com.alicloud.openservices.tablestore.SyncClient;
import com.alicloud.openservices.tablestore.model.*;
import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.SearchRequest;
import com.alicloud.openservices.tablestore.model.search.SearchResponse;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.query.TermsQuery;
import com.ebon.energy.fms.common.enums.CloudPlatformName;
import com.ebon.energy.fms.config.AliTableStoreConfig;
import com.ebon.energy.fms.domain.vo.RedbackTableStorageEntity;
import com.ebon.energy.fms.domain.vo.TelemetryDataVO;
import com.ebon.energy.fms.domain.vo.telemetry.RossTelemetry;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.domain.vo.telemetry.TelemetryExtensions;
import com.ebon.energy.fms.service.TableStoreService;
import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.util.TimeUtils;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.groupBy;
import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Slf4j
@Service
public class AliyunTableStoreService implements TableStoreService {

    @Resource
    private AliTableStoreConfig aliTableStoreConfig;

    public AliyunTableStoreService() {
        // 初始化存储仓库日期配置
        initializeStorageRepositoryDates(
            false, // enableSavingToNewTelemetryStorageAccount
            FAR_FUTURE_DATE, // useOnlyNewTelemetryFromDateyyyyMMdd
            FAR_FUTURE_DATE  // useOnlyOldStoragePriorToDateyyyyMMdd
        );
    }

    private static final String OLD_STORAGE_TABLE_NAME = "inverterdata";
    private static final String TELEMETRY_TABLE_NAME = "telemetrydata";
    private static final String TELEMETRY_INDEX = "telemetrydata_index";
    private static final String FAR_FUTURE_DATE = "********";

    private static final long SIXTY_DAYS_IN_SECONDS = TimeUnit.DAYS.toSeconds(60);
    private static boolean firstTime = true;

    private SyncClient inverterAliyunTable;
    private SyncClient telemetryAliyunTable;

    @Override
    public CloudPlatformName getCloudPlatformName() {
        return CloudPlatformName.Tuya;
    }

    @Override
    public TelemetryDataVO getTelemetryData(String sn, long epoch) {
        if (epoch > 946684800000l) // This is Y2K in milliseconds and 1st of April 31,969 in seconds
        {
            // 2020 May
            // When using point-to-system-details on charts the epoch that is used
            // to load SystemDetails is the milliseconds epoch.
            // It worked because a query between epoch-1 and epoch+1 to get a system status at a point
            // - for example a query from System Details would be
            //  ((RowKey gt '1588096352999') and(RowKey lt '1588096353001')) -
            // and greater_than and less_than use lexicographical order so
            // 1588096352999 < 1588096352 < 1588096353001

            if (epoch % 1000 == 0) // Belt and bracers
            {
                epoch /= 1000;
            }
        }

        SyncClient client = getTelemetryAliyunTable();

        //创建API请求并设置参数。
        //构造主键。
        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
        primaryKeyBuilder.addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(sn));
        primaryKeyBuilder.addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(String.format("%010d", Long.MAX_VALUE - epoch)));
        PrimaryKey primaryKey = primaryKeyBuilder.build();

        Row row = null;
        if (epoch >= aliTableStoreConfig.getUseOnlyOldStoragePriorToEpoch()) {
            //读取一行数据，设置数据表名称。
            SingleRowQueryCriteria criteria = new SingleRowQueryCriteria("telemetrydata", primaryKey);
            //设置读取最新版本。
            criteria.setMaxVersions(1);
            //设置读取某些列。
            //criteria.addColumnsToGet("RossTelemetry");
            GetRowRequest getRowRequest = new GetRowRequest(criteria);

            //发起请求并打印返回结果。
            GetRowResponse getRowResponse = client.getRow(getRowRequest);
            row = getRowResponse.getRow();
        }

        if (row == null && epoch <= aliTableStoreConfig.getUseOnlyNewTelemetryFromEpoch()) {
            //读取一行数据，设置数据表名称。
            SingleRowQueryCriteria inverterCriteria = new SingleRowQueryCriteria("inverterdata", primaryKey);
            //设置读取最新版本。
            inverterCriteria.setMaxVersions(1);
            GetRowRequest inverterRowRequest = new GetRowRequest(inverterCriteria);

            //发起请求并打印返回结果。
            GetRowResponse inverterRowResponse = client.getRow(inverterRowRequest);
            row = inverterRowResponse.getRow();
        }

        TelemetryDataVO dataVO = new TelemetryDataVO();
        dataVO.setSerialNumber(sn);

        if (row == null) {
            return dataVO;
        }

        Column telemetryCol = row.getLatestColumn("RossTelemetry");
        Column documentCol = row.getLatestColumn("Document");
        if (telemetryCol != null) {
            dataVO.setTelemetry((String) telemetryCol.getValue().getValue());
        }
        if (documentCol != null) {
            dataVO.setSystemStatus((String) documentCol.getValue().getValue());
        }
        return dataVO;
    }

    public List<TelemetryDataVO> getDataInRangeByKeys(List<String> partitionKeys, long startEpoch, long endEpoch) {
        List<RedbackTableStorageEntity> datas = getDataInRangeBatch(partitionKeys, startEpoch, endEpoch);
        return mapList(datas, e -> {
            TelemetryDataVO dataVO = new TelemetryDataVO();
            dataVO.setSerialNumber(e.getPartitionKey());
            dataVO.setTelemetryVO(convertToTelemetry(e.getFullRossTelemetry()));
            dataVO.setSystemStatusVO(convertDocumentToSystemStatus(e.getDocument()));
            return dataVO;
        });
    }

    public List<RossTelemetry> getTelemetryInRangeBatch(List<String> partitionKeys, long startEpoch, long endEpoch) {
        List<RedbackTableStorageEntity> datas = getDataInRangeBatch(partitionKeys, startEpoch, endEpoch);
        return mapList(datas, e -> convertToTelemetry(e.getFullRossTelemetry()));
    }

    public List<SystemStatus> getSystemStatusInRangeBatch(List<String> partitionKeys, long startEpoch, long endEpoch) {
        List<RedbackTableStorageEntity> datas = getDataInRangeBatch(partitionKeys, startEpoch, endEpoch);
        return mapList(datas, e -> convertDocumentToSystemStatus(e.getDocument()));
    }

    /**
     * 批量key范围查询（适用新数据，未做时间段判断逻辑）
     * 
     * @param partitionKeys 
     * @param startEpoch
     * @param endEpoch
     * @return
     */
    public List<RedbackTableStorageEntity> getDataInRangeBatch(List<String> partitionKeys, long startEpoch, long endEpoch) {
        List<RedbackTableStorageEntity> result = new ArrayList<>();

        List<ColumnValue> terms = mapList(partitionKeys, e -> ColumnValue.fromString(e));

        SyncClient client = getTelemetryAliyunTable();

        // 创建 TermsQuery 用于 PartitionKey 的 IN 查询
        TermsQuery partitionKeyQuery = new TermsQuery();
        partitionKeyQuery.setFieldName("PartitionKey");
        partitionKeyQuery.setTerms(terms);

        String startRowKey = getRowKey(Long.MAX_VALUE - startEpoch);
        String endRowKey = getRowKey(Long.MAX_VALUE - endEpoch);

        // 创建 RangeQuery 用于 RowKey 的范围查询
        RangeQuery rowKeyQuery = new RangeQuery();
        rowKeyQuery.setFieldName("RowKey");
        rowKeyQuery.setFrom(ColumnValue.fromString(endRowKey));
        rowKeyQuery.setTo(ColumnValue.fromString(startRowKey));

        // 使用 BoolQuery 将两个查询条件组合起来
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(Arrays.asList(partitionKeyQuery, rowKeyQuery));

        // 设置 SearchQuery
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(boolQuery);
        searchQuery.setLimit(100); // 设置返回的最大条数

        SearchRequest.ColumnsToGet columns = new SearchRequest.ColumnsToGet();
        columns.setColumns(Arrays.asList("Document", "RossTelemetry", "RossTelemetry2"));

        // 执行查询
        SearchRequest request = new SearchRequest();
        request.setColumnsToGet(columns);
        request.setTableName(TELEMETRY_TABLE_NAME);
        request.setIndexName(TELEMETRY_INDEX);
        request.setSearchQuery(searchQuery);
        SearchResponse response = client.search(request);

        // 处理第一页的结果
        List<Row> rows = response.getRows();
        // 如果有更多结果，继续查询
        while (response.getNextToken() != null) {
            request.getSearchQuery().setToken(response.getNextToken());
            response = client.search(request);
            rows.addAll(response.getRows());
        }

        // 处理结果
        for (Row row : response.getRows()) {
            RedbackTableStorageEntity entity = convertRowToEntity(row);
            if (entity != null) {
                result.add(entity);
            }
        }

        return result;
    }

    private RangeRowQueryCriteria prepareTelemetryAliyunQuery(
            String serialNumber,
            long startEpoch,
            long endEpoch,
            boolean newStorageFormat,
            String tableName) {

        // 验证时间范围不超过60天
        if ((endEpoch - startEpoch) > SIXTY_DAYS_IN_SECONDS) {
            throw new IllegalArgumentException("The query timespan is too big. Please use a shorter span.");
        }

        if (newStorageFormat) {
            // 新存储格式 - 使用倒序时间戳
            String startRowKey = getRowKey(Long.MAX_VALUE - startEpoch);
            String endRowKey = getRowKey(Long.MAX_VALUE - endEpoch);

            PrimaryKey primaryKeyStart = PrimaryKeyBuilder.createPrimaryKeyBuilder()
                    .addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(serialNumber))
                    .addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(endRowKey))
                    .build();

            PrimaryKey primaryKeyEnd = PrimaryKeyBuilder.createPrimaryKeyBuilder()
                    .addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(serialNumber))
                    .addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(startRowKey))
                    .build();

            RangeRowQueryCriteria rangeQueryCriteria = new RangeRowQueryCriteria(tableName);
            rangeQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyStart);
            rangeQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyEnd);
            rangeQueryCriteria.setMaxVersions(1);
            rangeQueryCriteria.setLimit(1440);

            return rangeQueryCriteria;
        } else {
            // 旧存储格式 - 使用正序时间戳
            String startRowKey = getRowKey(startEpoch);
            String endRowKey = getRowKey(endEpoch);

            PrimaryKey primaryKeyStart = PrimaryKeyBuilder.createPrimaryKeyBuilder()
                    .addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(serialNumber))
                    .addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(startRowKey))
                    .build();

            PrimaryKey primaryKeyEnd = PrimaryKeyBuilder.createPrimaryKeyBuilder()
                    .addPrimaryKeyColumn("PartitionKey", PrimaryKeyValue.fromString(serialNumber))
                    .addPrimaryKeyColumn("RowKey", PrimaryKeyValue.fromString(endRowKey))
                    .build();

            RangeRowQueryCriteria rangeQueryCriteria = new RangeRowQueryCriteria(tableName);
            rangeQueryCriteria.setInclusiveStartPrimaryKey(primaryKeyStart);
            rangeQueryCriteria.setExclusiveEndPrimaryKey(primaryKeyEnd);
            rangeQueryCriteria.setMaxVersions(1);
            rangeQueryCriteria.setLimit(1440);

            return rangeQueryCriteria;
        }
    }

    private static String getRowKey(long epoch) {
        return String.format("%010d", epoch);
    }

    /**
     * 同步版本的数据范围查询方法
     */
    public List<SystemStatus> getDataInRange(String serialNumber, long startEpoch, long endEpoch) {
        try {
            return getDataInRangeAsync(serialNumber, startEpoch, endEpoch).get();
        } catch (Exception e) {
            log.error("同步获取数据范围失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 异步获取指定时间范围内的系统状态数据
     * @param serialNumber 设备序列号
     * @param startEpoch 开始时间戳(秒)
     * @param endEpoch 结束时间戳(秒)
     * @return CompletableFuture包装的SystemStatus列表
     */
    public CompletableFuture<List<SystemStatus>> getDataInRangeAsync(String serialNumber, long startEpoch, long endEpoch) {
        return getInRangeAsync(
            serialNumber,
            startEpoch,
            endEpoch,
            Arrays.asList("Document"),
            entity -> entity.getDocument() != null,
            entity -> convertDocumentToSystemStatus(entity.getDocument())
        );
    }

    /**
     * 将Document JSON字符串转换为SystemStatus对象
     */
    private SystemStatus convertDocumentToSystemStatus(String document) {
        if (document == null || document.trim().isEmpty()) {
            return null;
        }
        try {
            return JSONObject.parseObject(document, SystemStatus.class);
        } catch (Exception e) {
            log.error("转换Document到SystemStatus失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 同步版本的遥测数据范围查询方法
     */
    public List<RossTelemetry> getTelemetryInRange(String serialNumber, long startEpoch, long endEpoch) {
        try {
            return getTelemetryInRangeAsync(serialNumber, startEpoch, endEpoch).get();
        } catch (Exception e) {
            log.error("同步获取数据范围失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 异步获取指定时间范围内的遥测数据
     * @param serialNumber 设备序列号
     * @param startEpoch 开始时间戳(秒)
     * @param endEpoch 结束时间戳(秒)
     * @return CompletableFuture包装的RossTelemetry列表
     */
    public CompletableFuture<List<RossTelemetry>> getTelemetryInRangeAsync(String serialNumber, long startEpoch, long endEpoch) {
        return getInRangeAsync(
                serialNumber,
                startEpoch,
                endEpoch,
                Arrays.asList("RossTelemetry", "RossTelemetry2"),
                entity -> entity.getRossTelemetry() != null,
                entity -> convertToTelemetry(entity.getRossTelemetry() + Strings.nullToEmpty(entity.getRossTelemetry2()))
        );
    }

    /**
     * 将RossTelemetry JSON字符串转换为RossTelemetry对象
     */
    private RossTelemetry convertToTelemetry(String telemetry) {
        if (telemetry == null || telemetry.trim().isEmpty()) {
            return null;
        }
        try {
            RossTelemetry rossTelemetry = JSONObject.parseObject(telemetry, RossTelemetry.class);
            TelemetryExtensions.rebuildDataBackedBands(rossTelemetry.getRossDeviceTelemetry());
            return rossTelemetry;
        } catch (Exception e) {
            log.error("转换到RossTelemetry失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 通用的异步范围查询方法
     * @param serialNumber 设备序列号
     * @param startEpoch 开始时间戳
     * @param endEpoch 结束时间戳
     * @param columns 要查询的列
     * @param predicate 过滤条件
     * @param selector 结果转换函数
     * @return CompletableFuture包装的结果列表
     */
    private <T> CompletableFuture<List<T>> getInRangeAsync(
            String serialNumber,
            long startEpoch,
            long endEpoch,
            List<String> columns,
            Predicate<RedbackTableStorageEntity> predicate,
            Function<RedbackTableStorageEntity, T> selector) {

        return CompletableFuture.supplyAsync(() -> {
            List<RedbackTableStorageEntity> newData = new ArrayList<>();
            List<RedbackTableStorageEntity> oldData = new ArrayList<>();

            // 从新存储获取数据（如果有数据在旧存储截止日期之后）
            if (endEpoch >= aliTableStoreConfig.getUseOnlyOldStoragePriorToEpoch()) {
                try {
                    newData = queryFromNewStorage(serialNumber, startEpoch, endEpoch, columns);
                } catch (Exception ex) {
                    log.error("查询新存储失败: {}", ex.getMessage(), ex);
                }
            }

            // 从旧存储获取数据（如果有数据在新存储部署之前）
            if (startEpoch <= aliTableStoreConfig.getUseOnlyNewTelemetryFromEpoch()) {
                try {
                    oldData = queryFromOldStorage(serialNumber, startEpoch, endEpoch, columns);
                } catch (Exception ex) {
                    log.error("查询旧存储失败: {}", ex.getMessage(), ex);
                }
            }

            // 根据时间范围决定返回哪些数据
            if (startEpoch > aliTableStoreConfig.getUseOnlyNewTelemetryFromEpoch()) {
                // 仅新数据
                return newData.stream()
                    .filter(predicate)
                    .map(selector)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            } else if (endEpoch < aliTableStoreConfig.getUseOnlyOldStoragePriorToEpoch()) {
                // 仅旧数据
                return oldData.stream()
                    .filter(predicate)
                    .map(selector)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            } else {
                // 混合新旧数据，需要合并
                return mergeOldAndNewData(oldData, newData, predicate, selector);
            }
        });
    }

    /**
     * 从新存储查询数据
     */
    private List<RedbackTableStorageEntity> queryFromNewStorage(String serialNumber, long startEpoch, long endEpoch, List<String> columns) {
        List<RedbackTableStorageEntity> result = new ArrayList<>();

        try {
            RangeRowQueryCriteria query = prepareTelemetryAliyunQuery(
                serialNumber, startEpoch, endEpoch, true, TELEMETRY_TABLE_NAME);

            GetRangeRequest request = new GetRangeRequest(query);
            GetRangeResponse response;

            do {
                response = getTelemetryAliyunTable().getRange(request);

                for (Row row : response.getRows()) {
                    RedbackTableStorageEntity entity = convertRowToEntity(row);
                    if (entity != null) {
                        result.add(entity);
                    }
                }

                // 检查是否还有下一页数据
                if (response.getNextStartPrimaryKey() != null) {
                    query.setInclusiveStartPrimaryKey(response.getNextStartPrimaryKey());
                    request = new GetRangeRequest(query);
                }

            } while (response.getNextStartPrimaryKey() != null);

        } catch (Exception e) {
            log.error("查询新存储表失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 从旧存储查询数据
     */
    private List<RedbackTableStorageEntity> queryFromOldStorage(String serialNumber, long startEpoch, long endEpoch, List<String> columns) {
        List<RedbackTableStorageEntity> result = new ArrayList<>();

        try {
            RangeRowQueryCriteria query = prepareTelemetryAliyunQuery(
                serialNumber, startEpoch, endEpoch, false, OLD_STORAGE_TABLE_NAME);

            GetRangeRequest request = new GetRangeRequest(query);
            GetRangeResponse response;

            do {
                response = getInverterAliyunTable().getRange(request);

                for (Row row : response.getRows()) {
                    RedbackTableStorageEntity entity = convertRowToEntity(row);
                    if (entity != null) {
                        result.add(entity);
                    }
                }

                // 检查是否还有下一页数据
                if (response.getNextStartPrimaryKey() != null) {
                    query.setInclusiveStartPrimaryKey(response.getNextStartPrimaryKey());
                    request = new GetRangeRequest(query);
                }

            } while (response.getNextStartPrimaryKey() != null);

        } catch (Exception e) {
            log.error("查询旧存储表失败: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 合并新旧数据
     */
    private <T> List<T> mergeOldAndNewData(
            List<RedbackTableStorageEntity> oldData,
            List<RedbackTableStorageEntity> newData,
            Predicate<RedbackTableStorageEntity> predicate,
            Function<RedbackTableStorageEntity, T> selector) {

        List<T> combinedList = new ArrayList<>();

        // 简化版本：先添加旧数据，再添加新数据，去重由时间戳处理
        oldData.stream()
            .filter(predicate)
            .map(selector)
            .filter(Objects::nonNull)
            .forEach(combinedList::add);

        newData.stream()
            .filter(predicate)
            .map(selector)
            .filter(Objects::nonNull)
            .forEach(combinedList::add);

        return combinedList;
    }

    /**
     * 将TableStore的Row转换为RedbackTableStorageEntity
     */
    private RedbackTableStorageEntity convertRowToEntity(Row row) {
        if (row == null || row.getPrimaryKey() == null) {
            return null;
        }

        try {
            RedbackTableStorageEntity entity = new RedbackTableStorageEntity();

            // 设置主键 - 使用正确的方法获取主键值
            PrimaryKeyColumn partitionKeyColumn = row.getPrimaryKey().getPrimaryKeyColumn("PartitionKey");
            PrimaryKeyColumn rowKeyColumn = row.getPrimaryKey().getPrimaryKeyColumn("RowKey");

            if (partitionKeyColumn != null) {
                entity.setPartitionKey(partitionKeyColumn.getValue().asString());
            }
            if (rowKeyColumn != null) {
                entity.setRowKey(rowKeyColumn.getValue().asString());
            }

            // 设置属性列
            for (Column column : row.getColumns()) {
                String columnName = column.getName();
                ColumnValue value = column.getValue();

                if (value != null && value.getValue() != null) {
                    switch (columnName) {
                        case "Document":
                            entity.setDocument(value.asString());
                            break;
                        case "Generation":
                            if (value.getValue() instanceof String) {
                                entity.setGeneration(Integer.valueOf(value.asString()));
                            } else {
                                entity.setGeneration((int) value.asLong());
                            }
                            break;
                        case "RossTelemetry":
                            entity.setRossTelemetry(value.asString());
                            break;
                        case "RossTelemetry2":
                            entity.setRossTelemetry2(value.asString());
                            break;
                        case "DeviceTelemetry":
                            entity.setDeviceTelemetry(value.asString());
                            break;
                        case "DeviceTelemetry2":
                            entity.setDeviceTelemetry2(value.asString());
                            break;
                    }
                }
            }

            return entity;
        } catch (Exception e) {
            log.error("转换Row到Entity失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 初始化存储仓库日期配置
     */
    private void initializeStorageRepositoryDates(
            boolean enableSavingToNewTelemetryStorageAccount,
            String useOnlyNewTelemetryFromDateyyyyMMdd,
            String useOnlyOldStoragePriorToDateyyyyMMdd) {

        try {
            // 简化版本：使用固定的epoch值
            // 在实际应用中，这些值应该从配置中读取
            long farFutureEpoch = 1893456000L; // 2030年的某个时间

            if (firstTime) {
                log.info("StorageRepository初始化完成: enableSavingToNewTelemetryStorageAccount={}",
                    enableSavingToNewTelemetryStorageAccount);
                firstTime = false;
            }
        } catch (Exception e) {
            log.error("初始化存储仓库日期配置失败: {}", e.getMessage(), e);
        }
    }

    // 获取阿里云TableStore客户端 - Inverter表
    private SyncClient getInverterAliyunTable() {
        if (inverterAliyunTable == null) {
            String endPoint = aliTableStoreConfig.getEndPoint();
            String accessKeyId = aliTableStoreConfig.getAccessKey();
            String accessKeySecret = aliTableStoreConfig.getSecretKey();
            String instanceName = aliTableStoreConfig.getInstanceName();
            inverterAliyunTable = new SyncClient(endPoint, accessKeyId, accessKeySecret, instanceName);
        }
        return inverterAliyunTable;
    }

    // 获取阿里云TableStore客户端 - Telemetry表
    private SyncClient getTelemetryAliyunTable() {
        if (telemetryAliyunTable == null) {
            String endPoint = aliTableStoreConfig.getEndPoint();
            String accessKeyId = aliTableStoreConfig.getAccessKey();
            String accessKeySecret = aliTableStoreConfig.getSecretKey();
            String instanceName = aliTableStoreConfig.getInstanceName();
            telemetryAliyunTable = new SyncClient(endPoint, accessKeyId, accessKeySecret, instanceName);
        }
        return telemetryAliyunTable;
    }

    public static void main(String[] args) {
        Instant t1 = Instant.parse("2025-07-30T02:00:00Z");
        Instant t2 = Instant.parse("2025-07-30T02:10:00Z");
        String startRowKey = getRowKey(Long.MAX_VALUE - t1.getEpochSecond());
        String endRowKey = getRowKey(Long.MAX_VALUE - t2.getEpochSecond());
        System.out.println(startRowKey);
        System.out.println(endRowKey);
    }
    
}