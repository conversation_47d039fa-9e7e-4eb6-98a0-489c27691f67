package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.utils.SiteOverviewHelper;
import com.ebon.energy.fms.domain.entity.DailyTotalVO;
import com.ebon.energy.fms.domain.vo.charts.ChartDataPoint;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.DataForDashboardVO;
import com.ebon.energy.fms.domain.vo.WattHour;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.service.site.SiteChartsDataService;
import io.vavr.Tuple2;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class SingleDeviceSite implements ISiteConfigurationAggregator {

    @Override
    public AllAboutSiteOverview processSiteOverview(String publicSiteId, List<AllAboutDevice> siteDevices) {
        if (CollectionUtils.isEmpty(siteDevices)) {
            return null;
        }

        // 获取第一个设备（如果存在）
        AllAboutDevice theDevice = siteDevices.get(0);

        // 处理可空属性，设置默认值
        boolean hasSupportForConnectedPV = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isSupportsConnectedPV)
                .orElse(true);  // 默认值为true

        boolean hasSolar = theDevice.getHasSolar();
        boolean hasBatteries = theDevice.getHasBatteries();
        LocalDate supportsLoadContributorsSince = theDevice.getSupportsLoadContributorsSince();

        DataForDashboardVO siteStatus = theDevice.getDataForDashboard();
        WattHour maximumPossiblePVOutput = theDevice.getMaximumPossiblePVOutput();
        boolean batteryMismatchProtectionEnabled = theDevice.getIsBatteryMismatchProtectionEnabled();

        boolean measuringThirdPartyInverter = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isMeasuringThirdPartyInverter)
                .orElse(false);

        boolean isAcCoupledMode = Optional.ofNullable(theDevice.getSpecification())
                .map(InstallationSpecification::isInAcCoupledMode)
                .orElse(false);

        // 构建并返回站点概览对象
        return new AllAboutSiteOverview(
                publicSiteId,
                siteStatus,
                siteDevices,
                theDevice.getTodaysDateInLocalTime(),
                hasSupportForConnectedPV,
                maximumPossiblePVOutput,
                hasSolar,
                hasBatteries,
                batteryMismatchProtectionEnabled,
                supportsLoadContributorsSince,
                measuringThirdPartyInverter,
                isAcCoupledMode
        );
    }

    @Override
    public List<DailyTotalVO> processSiteNDayHistory(String publicSiteId, List<Tuple2<AllAboutDevice, List<DailyTotalVO>>> deviceHistories, int twiceDays) {
        return deviceHistories.stream().findFirst().get()._2;
    }

    @Override
    public BatteryStatusDto processSiteBatteryStatus(AllAboutSiteOverview allAboutSiteOverview, EnergyFlowDto siteFlow, int numberOfConsecutiveDaysBatteriesMismatchedToDisabled) {
        // 获取第一个设备（如果存在）
        AllAboutDevice theDevice = allAboutSiteOverview.getDeviceDetails().stream()
                .findFirst()
                .orElse(null);

        if (theDevice == null) {
            return null;
        }

        // 计算电池禁用日期
        String dateBatteriedWillBeDisabled = SiteOverviewHelper.getDateTimeDisabled(
                theDevice.getIsBatteryMismatchProtectionEnabled() != null && theDevice.getIsBatteryMismatchProtectionEnabled()
                        ? (theDevice.getDataForDashboard() != null && theDevice.getDataForDashboard().getBatterySummary() != null
                        ? theDevice.getDataForDashboard().getBatterySummary().getMismatchedTimeMinutes()
                        : null)
                        : null,
                numberOfConsecutiveDaysBatteriesMismatchedToDisabled,
                theDevice.getDataForDashboard() != null ? theDevice.getDataForDashboard().getTimeZone() : null,
                siteFlow.getDateLastStatusReceivedUtc());

        // 计算SoC (0-1范围)
        BigDecimal soC0to1 = BigDecimal.valueOf(
                (allAboutSiteOverview.getDataForDashboard() != null
                        && allAboutSiteOverview.getDataForDashboard().getBatterySummary() != null
                        && allAboutSiteOverview.getDataForDashboard().getBatterySummary().getSoC() != null)
                        ? allAboutSiteOverview.getDataForDashboard().getBatterySummary().getSoC() / 100.0
                        : 0.0);

        // 确定电池状态
        String state = siteFlow.isHasBatteries()
                ? (siteFlow.getBatteryStatus() != null ? siteFlow.getBatteryStatus() : "Unknown")
                : "No Battery";

        // 构造并返回BatteryStatusDto
        return new BatteryStatusDto(
                allAboutSiteOverview.getPublicSiteId(),
                siteFlow.isOnline(),
                siteFlow.getDateLastStatusReceivedUtc(),
                soC0to1,
                state,
                theDevice.getBatteryBackUp() != null
                        ? new BatteryBackUpDto(
                        theDevice.getBatteryBackUp().getHrsBackUp(),
                        theDevice.getBatteryBackUp().getBackUpSupport(),
                        theDevice.getBatteryBackUp().isSupportsConnectedPV(),
                        theDevice.getBatteryBackUp().getSerialNumber())
                        : null,
                siteFlow.isHasBatteries(),
                dateBatteriedWillBeDisabled);
    }

    @Override
    public BigDecimal processSiteRenewablePercent(String publicSiteId, List<ProductWithDailyCache> productsWithDailies) {
        return SiteOverviewHelper.getRenewablePercentageOnDifferentPhases(
                publicSiteId,
                productsWithDailies
        );
    }

    @Override
    public List<ChartDataPoint> processSiteChartData(String siteId, List<SiteChartsDataService.ProductWithChartData> siteProductData, long startEpoch, long endEpoch, long siteChartMaxLookAheadAllowanceInSec) {
        return siteProductData.stream().findFirst().map(e -> e.getChartData()).orElse(new ArrayList<>());
    }
}
