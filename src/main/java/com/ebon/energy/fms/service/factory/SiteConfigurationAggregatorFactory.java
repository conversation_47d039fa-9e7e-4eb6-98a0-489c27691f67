package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.Phase;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.SiteConfiguration;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.site.AllAboutDevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SiteConfigurationAggregatorFactory {

    public ISiteConfigurationAggregator getSiteAggregator(String publicSiteId, List<AllAboutDevice> siteDevices) {
        SiteConfiguration config = getConfigurationIdForSiteDevices(siteDevices);

        if (config == SiteConfiguration.SingleDeviceSite) {
            return new SingleDeviceSite();
        } else if (config == SiteConfiguration.MultiSiteAllDifferentPhases) {
            return new MultiSiteAllDifferentPhases();
        } else if (config == SiteConfiguration.MultiSiteAllSamePhase) {
            return new MultiSiteAllSamePhase();
        }
        throw new BizException("Unsupported site configuration: " + config);
    }

    private SiteConfiguration getConfigurationIdForSiteDevices(List<AllAboutDevice> siteDevices) {
        if (siteDevices.size() == 1) {
            return SiteConfiguration.SingleDeviceSite;
        }

        // 所有设备与第一个设备使用相同相位
        Phase firstPhase = siteDevices.get(0).getPhase();
        if (siteDevices.stream().allMatch(d -> d.getPhase() == firstPhase)) {
            // 且恰好有一个协调器
            long coordinatorCount = siteDevices.stream()
                    .filter(d -> d.getPhaseRole() == PhaseRole.Coordinator)
                    .count();

            if (coordinatorCount != 1) {
                throw new BizException("Invalid site configuration. Must have exactly one Coordinator per phase.");
            }

            return SiteConfiguration.MultiSiteAllSamePhase;
        }

        // 对于所有使用的相位，每个相位上只有一个设备
        Map<Phase, Long> phaseCounts = siteDevices.stream()
                .collect(Collectors.groupingBy(AllAboutDevice::getPhase, Collectors.counting()));

        if (phaseCounts.values().stream().allMatch(count -> count == 1)) {
            return SiteConfiguration.MultiSiteAllDifferentPhases;
        }

        // 暂不支持的配置
        throw new BizException("Your site configuration is currently unsupported.");
    }
}