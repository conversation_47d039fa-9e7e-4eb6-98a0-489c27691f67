package com.ebon.energy.fms.service.installer;

import com.ebon.energy.fms.domain.vo.installer.FleetHealthSummaryViewModel;
import com.ebon.energy.fms.domain.vo.installer.InstallationSummaryViewModel;
import com.ebon.energy.fms.repository.IInstallerSummaryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class InstallationService {

    private final IInstallerSummaryRepository iInstallerSummaryRepository;


    public List<FleetHealthSummaryViewModel> getFleetHealthSummaryAsync(String installerId) {
        return iInstallerSummaryRepository.readFleetHealthSummaryAsync(installerId);
    }

    public InstallationSummaryViewModel installationSummary(String installerId) {
        return iInstallerSummaryRepository.readInstallationSummaryAsync(installerId);
    }

    public List<String> getModelsName() {
        return iInstallerSummaryRepository.getModelsName();
    }
}
