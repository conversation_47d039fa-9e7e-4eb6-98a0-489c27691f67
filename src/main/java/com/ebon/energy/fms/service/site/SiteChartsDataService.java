package com.ebon.energy.fms.service.site;

import com.ebon.energy.fms.common.utils.ChartsUtility;
import com.ebon.energy.fms.controller.request.ChartsPlotOptionsRequest;
import com.ebon.energy.fms.controller.request.ChartsSitePlotOptionsRequest;
import com.ebon.energy.fms.domain.vo.charts.ChartDataPoint;
import com.ebon.energy.fms.domain.vo.charts.ChartsDataBandVO;
import com.ebon.energy.fms.domain.vo.charts.ChartsMetricConfiguration;
import com.ebon.energy.fms.domain.vo.site.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.site.AllAboutSiteOverview;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.service.SiteDeviceService;
import com.ebon.energy.fms.service.charts.ChartsService;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Site Charts Data Service
 * Handles data retrieval for site charts
 */
@Slf4j
@Service
public class SiteChartsDataService {

    @Resource
    private SiteDeviceService siteDeviceService;

    @Resource
    private SiteConfigurationAggregatorFactory siteConfigurationAggregatorFactory;

    @Resource
    private ProductDbRepository productDbRepository;

    @Resource
    private RedbackRepository redbackRepository;

    @Resource
    private ChartsService chartsService;

    /**
     * Get chart data points for a site
     *
     * @param options                             request options
     * @param timezone                            time zone
     * @param maxDaysDateRange                    max days date range
     * @param siteChartMaxLookAheadAllowanceInSec max look ahead allowance in seconds
     * @return map of grouped data points
     */
    public Map<String, List<ChartDataPoint>> getChartDataPointsForSite(
            ChartsSitePlotOptionsRequest options,
            ZoneId timezone,
            int maxDaysDateRange,
            long siteChartMaxLookAheadAllowanceInSec) {

        try {
            // Get site devices
            List<AllAboutDevice> siteDevices = siteDeviceService.getSiteDevices(options.getSiteId());

            // Get site aggregator
            ISiteConfigurationAggregator siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(options.getSiteId(), siteDevices);

            // Process site overview
            AllAboutSiteOverview allAbout = siteAggregator.processSiteOverview(options.getSiteId(), siteDevices);

            // Parse date range
            var dateRange = ChartsUtility.getStartAndEndDate(options.getStartDate(), options.getEndDate(), options.getTimeZone());
            long startEpoch = dateRange.getStart();
            long endEpoch = dateRange.getEnd();

            // Limit the Max days range - dividing it by number of inverters + system (Reduce the data points to display on the site chart)
            int restrictedMaxDaysDateRange = (int) Math.floor((double) maxDaysDateRange / (siteDevices.size() + 1));
            restrictedMaxDaysDateRange = restrictedMaxDaysDateRange != 0 ? restrictedMaxDaysDateRange : 1;

            if (restrictedMaxDaysDateRange >= 0) {
                int secondsInDay = 86400;
                int maxNumSecondsInRange = secondsInDay * restrictedMaxDaysDateRange;

                // check if request is for too long a time period
                if (endEpoch > startEpoch && ((endEpoch - startEpoch) >= maxNumSecondsInRange)) {
                    // shorten the date range
                    endEpoch = startEpoch + maxNumSecondsInRange;
                }
            }

            // Create chart configuration from site overview
            ChartsMetricConfiguration chartConfig = chartConfigFromSiteOverview(allAbout);

            // Get product data with chart data in parallel
            List<ProductWithChartData> siteProductData = getProductWithChartDataInParallel(
                    chartConfig, siteDevices, startEpoch, endEpoch);

            // Process site data points
            var siteDataPoints = siteAggregator.processSiteChartData(options.getSiteId(),
                    siteProductData,
                    startEpoch,
                    endEpoch,
                    siteChartMaxLookAheadAllowanceInSec);

            // Build result map
            Map<String, List<ChartDataPoint>> dataPoints = new HashMap<>();
            dataPoints.put("System", siteDataPoints);

            // Add individual device data
            for (ProductWithChartData productData : siteProductData) {
                dataPoints.put(productData.getDevice().getSerialNumber(), productData.getChartData());
            }

            return dataPoints;

        } catch (Exception e) {
            log.error("Failed to get chart data for site: {}", options.getSiteId(), e);
            throw new RuntimeException("Failed to get chart data for site", e);
        }
    }

    /**
     * Get chart data points for a single inverter
     *
     * @param options request options containing serial number, date range, etc.
     * @return chart data band with plot points
     */
    public ChartsDataBandVO getChartDataForInverter(ChartsPlotOptionsRequest options) {
        // Delegate to the existing ChartsService dataBand method
        return chartsService.dataBand(options);
    }

    /**
     * Get product data with chart data in parallel
     */
    private List<ProductWithChartData> getProductWithChartDataInParallel(
            ChartsMetricConfiguration chartConfig,
            List<AllAboutDevice> devices,
            long startEpoch,
            long endEpoch) {

        int batchSizeLimit = 5;
        List<ProductWithChartData> siteProductData = new ArrayList<>();

        if (devices.size() <= batchSizeLimit) {
            siteProductData = getSiteProductData(chartConfig, devices, startEpoch, endEpoch);
        } else {
            // Process in batches of 5
            for (int i = 0; i < devices.size(); i += batchSizeLimit) {
                int end = Math.min(i + batchSizeLimit, devices.size());
                List<AllAboutDevice> batch = devices.subList(i, end);
                siteProductData.addAll(getSiteProductData(chartConfig, batch, startEpoch, endEpoch));
            }
        }

        return siteProductData;
    }

    /**
     * Get site product data for a batch of devices
     */
    private List<ProductWithChartData> getSiteProductData(
            ChartsMetricConfiguration chartConfig,
            List<AllAboutDevice> devices,
            long startEpoch,
            long endEpoch) {

        List<CompletableFuture<ProductWithChartData>> futures = devices.stream()
                .map(device -> CompletableFuture.supplyAsync(() ->
                        getProductWithChartData(chartConfig, device, startEpoch, endEpoch)))
                .collect(Collectors.toList());

        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    /**
     * Get product data with chart data for a single device
     */
    private ProductWithChartData getProductWithChartData(
            ChartsMetricConfiguration chartConfig,
            AllAboutDevice device,
            long startEpoch,
            long endEpoch) {

        try {
            int secondsInDay = 86400;
            List<SystemStatus> allSystemStatuses = new ArrayList<>();

            // Fetch data day by day
            for (long date = startEpoch - 1; date <= endEpoch - 1; date += secondsInDay) {
                long end = Math.min(date + secondsInDay, endEpoch);

                // Get system statuses for the date range
                List<SystemStatus> dayStatuses = productDbRepository.getSystemStatusesInRangeAsync(
                        device.getSerialNumber(), date, end);
                allSystemStatuses.addAll(dayStatuses);
            }

            // Sort by epoch
            allSystemStatuses.sort(Comparator.comparingLong(SystemStatus::getEpoch));

            // Convert to chart data points using chartConfig
            List<ChartDataPoint> chartDataPoints = allSystemStatuses.stream()
                    .map(ss -> new ChartDataPoint(ss, chartConfig))
                    .collect(Collectors.toList());

            // Get installation date from device
            LocalDateTime installationDate = device.getFirstSiteDevice() != null &&
                    device.getFirstSiteDevice().getInstallationDateUtc() != null ?
                    device.getFirstSiteDevice().getInstallationDateUtc().toLocalDateTime() : null;

            return new ProductWithChartData(device, installationDate, chartDataPoints);

        } catch (Exception e) {
            log.error("Failed fetching chart data for {} between {} and {}",
                    device.getSerialNumber(), startEpoch, endEpoch, e);
            throw new RuntimeException("Failed to get chart data for device", e);
        }
    }

    /**
     * Process site chart data - aggregate data from all devices
     */
    private List<ChartDataPoint> processSiteChartData(
            String siteId,
            List<ProductWithChartData> siteProductData,
            long startEpoch,
            long endEpoch,
            long siteChartMaxLookAheadAllowanceInSec) {

        // This is a simplified implementation
        // In a full implementation, this would aggregate data from all devices
        // and create a combined view of the site's performance

        Map<Long, ChartDataPoint> aggregatedData = new TreeMap<>();

        // Aggregate data from all devices
        for (ProductWithChartData productData : siteProductData) {
            for (ChartDataPoint dataPoint : productData.getChartData()) {
                Long epoch = dataPoint.getEpoch();

                // Check if data point is within allowed time range
                if (epoch >= startEpoch && epoch <= endEpoch) {
                    // Aggregate the data (simplified - in reality would sum/average values)
                    aggregatedData.merge(epoch, dataPoint, (existing, newData) -> {
                        // This is where you would implement the actual aggregation logic
                        // For now, just return the existing data
                        return existing;
                    });
                }
            }
        }

        return new ArrayList<>(aggregatedData.values());
    }

    /**
     * Create chart configuration from site overview
     */
    private ChartsMetricConfiguration chartConfigFromSiteOverview(AllAboutSiteOverview siteOverview) {
        if (siteOverview == null || siteOverview.getDataForDashboard() == null) {
            return null;
        }

        String timezone = siteOverview.getDataForDashboard().getTimeZone();
        boolean hasBattery = Boolean.TRUE.equals(siteOverview.getHasBatteries());
        boolean hasSolar = Boolean.TRUE.equals(siteOverview.getHasSolar());

        return new ChartsMetricConfiguration(timezone, hasBattery, hasSolar);
    }

    /**
     * Inner class to hold product data with chart data
     */
    public static class ProductWithChartData {
        private final AllAboutDevice device;
        private final LocalDateTime installationDateUtc;
        private final long installationDateEpoch;
        private final List<ChartDataPoint> chartData;

        public ProductWithChartData(AllAboutDevice device, LocalDateTime installationDateUtc, List<ChartDataPoint> chartData) {
            this.device = device;
            this.installationDateUtc = installationDateUtc;
            this.installationDateEpoch = installationDateUtc != null ?
                    installationDateUtc.toEpochSecond(ZoneOffset.UTC) : 0L;
            this.chartData = chartData;
        }

        public AllAboutDevice getDevice() {
            return device;
        }

        public LocalDateTime getInstallationDateUtc() {
            return installationDateUtc;
        }

        public long getInstallationDateEpoch() {
            return installationDateEpoch;
        }

        public List<ChartDataPoint> getChartData() {
            return chartData;
        }
    }


    public ChartsMetricConfiguration getChartConfigurationForSite(String siteId) {
        List<AllAboutDevice> siteDevices = siteDeviceService.getSiteDevices(siteId);

        // Get site aggregator
        ISiteConfigurationAggregator siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);

        // Process site overview
        AllAboutSiteOverview allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        return chartConfigFromSiteOverview(allAbout);
    }
}