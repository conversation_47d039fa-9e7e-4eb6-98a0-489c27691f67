package com.ebon.energy.fms.service.site;

import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.ChartsUtility;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.controller.request.ChartsSitePlotOptionsRequest;
import com.ebon.energy.fms.domain.vo.charts.*;
import com.ebon.energy.fms.service.RedbackJobHistoryService;
import com.ebon.energy.fms.util.ConfigurationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Site Charts Service
 */
@Slf4j
@Service
public class SiteChartsService {

    @Resource
    private RedbackJobHistoryService redbackJobHistoryService;

    @Resource
    private SiteChartsDataService siteChartsDataService;

    /**
     * Get plot data for site charts
     *
     * @param options chart options
     * @return plot data
     */
    public Map<String, Object> getPlotData(ChartsSitePlotOptionsRequest options) {
        try {
            // Validate user access - assuming this is handled by authentication/authorization

            // Get timezone
            ZoneId timezone = null;
            String tzTimeZone = null;
            if (options.getTimeZone() != null) {
                timezone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(options.getTimeZone());
                tzTimeZone = timezone.getId();
            }

            // Get max days date range from configuration
            int maxDaysDateRange = ConfigurationUtil.getPortalConfig().getMaxDaysDateRange();

            // Get site chart max look ahead allowance in seconds
            long siteChartMaxLookAheadAllowanceInSec = ConfigurationUtil.getPortalConfig().getSiteChartMaxLookAheadAllowanceInSec();

            // Get date range
            ChartsDateRange dateRange = ChartsUtility.getStartAndEndDate(
                    options.getStartDate(),
                    options.getEndDate(),
                    options.getTimeZone()
            );

            // Get storage reduction jobs
            List<RedbackJobHistory> reductionJobs = redbackJobHistoryService.getStorageReductionJobs();

            // Get bridge gap size
            long bridgeGapSize = ChartsUtility.getEpochBridgeGapSize(
                    dateRange.getStart(),
                    dateRange.getEnd(),
                    reductionJobs
            );

            // Get chart data points for site
            var dataPoints = siteChartsDataService.getChartDataPointsForSite(
                    options,
                    timezone,
                    maxDaysDateRange,
                    siteChartMaxLookAheadAllowanceInSec
            );

          var groupedPlotPoints =  dataPoints.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                            .map(ChartsSitePlotViewModel::new)
                            .collect(Collectors.toList())
            ));

            // Build response
            Map<String, Object> data = new HashMap<>();
            data.put("GroupedPlotPoints", groupedPlotPoints);
            data.put("Timezone", tzTimeZone);
            data.put("BridgeGapSize", bridgeGapSize);

            return data;
        } catch (Exception ex) {
            log.error("Error getting plot data for site: {}", options.getSiteId(), ex);
            throw new BizException("Failed to get plot data: " + ex.getMessage());
        }
    }

    public ChartsViewModel index(String siteId) {
        var chartConfigurationForSite = siteChartsDataService.getChartConfigurationForSite(siteId);
        return new ChartsViewModel(chartConfigurationForSite, ConfigurationUtil.getPortalConfig().getMaxDaysDateRange());
    }
}