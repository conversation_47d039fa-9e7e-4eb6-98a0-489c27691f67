package com.ebon.energy.fms.service.site;

import com.ebon.energy.fms.domain.vo.site.AllAboutDevice;
import com.ebon.energy.fms.domain.vo.site.AllAboutSiteOverview;
import com.ebon.energy.fms.domain.vo.site.BatteryStatusDto;
import com.ebon.energy.fms.domain.vo.EnergyFlowVO;
import com.ebon.energy.fms.service.SiteDeviceService;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SiteDashboardService {





}
